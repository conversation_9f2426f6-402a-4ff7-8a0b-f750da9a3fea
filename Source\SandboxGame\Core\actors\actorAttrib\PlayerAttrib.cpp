#pragma warning( disable : 4482 )
#include "ClientActor.h"
#include "ClientPlayer.h"
#include "DefManagerProxy.h"
#include "defdata.h"
#include "container.h"
#include "PlayerControl.h"
#include "backpack.h"
//#include "GameEvent.h"
#include "world.h"
#include "CameraModel.h"
#include "WorldManager.h"
#include "LuaInterfaceProxy.h"
#include "container_world.h"
#include "RuneDef.h"
#include "GameMode.h"
#include "MpActorManager.h"
#include "PlayerAttrib.h"
#include "ObserverEvent.h"
#include "ObserverEventManager.h"
#include "EffectManager.h"
#include "ActorAttribExecute.h"
#include "SoundComponent.h"
#include "CarryComponent.h"
#include "DropItemComponent.h"
#include "EffectComponent.h"
#include "ClientActorFuncWrapper.h"
#include "EffectComponent.h"
#include "PlayerViewMask.h"
#include "PlayerCheat.h"
#include "ActorPushSnowBall.h"
#include "PushSnowBallLocomotion.h"
#include "RiddenComponent.h"
#include "ActorBody.h"
#include "SandboxIdDef.h"
#include "BaseItemMesh.h"
#include "OgreModel.h"
#include "ActorLocoMotion.h"
#include "SandBoxManager.h"
#include "MapInfoRefreshCenter.h"
#include "HPValue.h"
#include "GameNetManager.h"
#include "SandboxListener.h"
#include "ThirstValue.h"
#include "FoodValue.h"
#include "CraftingQueue.h"
#include "PlayerDownedStateAttrib.h"
#include "Environment/TemperatureManager.h"
#include "Environment/RadiationManager.h"
#include "RadiationComponent.h"
#include "state/PlayerState.h"

#include <cmath>

#define ENABLE_DOWNSTATE 1
using namespace Rainbow;
using namespace MNSandbox;
const int PlayerAttrib::MIN_STILL_TICK = 20;
const int PlayerAttrib::MIN_STRENGTH_CONSUMPTION_TICK = 20;
IMPLEMENT_COMPONENTCLASS(PlayerAttrib)

// 模拟服务器发送消息给客户端
void sendQueueUpdateToClient(const std::vector<craft::Task>& queue, ClientPlayer* player) {

#ifndef IWORLD_SERVER_BUILD
	
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_CraftQueueChange", sandboxContext);
	}
	return;
	
#endif
	PB_CraftingQueueUpdateHC msg;

	for (const auto& task : queue) {
		PB_CraftingQueueTask* newTask = msg.add_tasks();
		newTask->set_crafting_id(task.craftingId);
		newTask->set_count(task.count);
		newTask->set_ticks_per_item(task.ticksPerItem);
		newTask->set_remaining_ticks(task.remainingTicks);
	}
	GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_CRAFTING_QUEUE_QUEUE_UPDATE_HC, msg);
	//std::cout << "[Server] Sending queue update to client: " << msg.tasks_size() << " tasks.\n";
}

// 模拟服务器通知客户端制作进度
void sendProgressUpdateToClient(const craft::Task& task) {
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_CraftQueueChange", sandboxContext);
	}
	//crafting::CraftingProgressUpdate msg;
	//msg.set_crafting_id(task.craftingId);
	//msg.set_remaining_ticks(task.remainingTicks);
	//msg.set_count(task.count);

	//std::cout << "[Server] Task " << task.craftingId << " Progress: "
	//	<< task.count << " left, " << task.remainingTicks << " ticks remaining.\n";
}

PlayerAttrib::PlayerAttrib(ClientPlayer* player)
{
	LOG_INFO("PlayerAttrib(): start");
	m_ClientPlayer = player;
	m_Backpack = SANDBOX_NEW(BackPack, m_ClientPlayer);
	m_CraftingQueue = SANDBOX_NEW(CraftingQueue, m_ClientPlayer,sendQueueUpdateToClient, sendProgressUpdateToClient);
	m_bInitialized = false;
	tickCount = 0;
	m_lastTickTime = 0;
	m_lastTickTimeClient = 0;
	m_lastRadiationRecoverTime = 0;
	m_lastFoodValue = 0;
	m_lastThirstValue = 0;

	initialize();

	m_StrengthExecute.push_back(ActorAttribType_Perseverance);
	m_StrengthExecute.push_back(ActorAttribType_Strength);
	m_ThirstExecute.push_back(ActorAttribType_Thirst);
	if (GetLuaInterfaceProxy().shouldUseNewHpRule())
	{
		m_attribExecute[ActorAttribType_Strength] = SANDBOX_NEW(ActorStrengthExecute);
	}
	m_attribExecute[ActorAttribType_Thirst] = SANDBOX_NEW(ActorThirstExecute);
	
	for (int i = 0; i < Actor_Speed_Type_Count; i++)
	{
		m_bSpeedProtected[i] = false;
	}
	CreateEvent2();
}

void PlayerAttrib::initialize()
{
	//LOG_INFO("initialize(): start");
	if (m_bInitialized)
	{
		return;
	}

	m_Attribs.resize(MAX_PLAYER_MODATTR);
	memset(&m_Attribs[0], 0, m_Attribs.size() * sizeof(AttribModified));

	ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();

	//血量
#ifdef OLD_ATTRIBUTES
	m_fBasicMaxHP = lua_const->hpmax;
#else
	mHPValue->SetBasicMaxHP(lua_const->hpmax);
#endif

	m_FoodValue = SANDBOX_NEW(FoodValue);
	m_FoodValue->SetMaxLimitValue(100);
	m_FoodValue->SetBasicMaxFood(100);
	m_FoodValue->SetValue(100);

	m_ThirstValue = SANDBOX_NEW(ThirstValue);
	m_ThirstValue->SetMaxLimitValue(100);
	m_ThirstValue->SetBasicMaxThirst(100);
	m_ThirstValue->SetValue(100);

	const PlayerAttribCsvDef* attrTempDef = GetDefManagerProxy()->getPlayerAttribCsvDef(PlayerAttributeType::Temperature);
	m_Temperature = attrTempDef->InitVal;
	m_FinalPosTemperature = m_Temperature;

	const PlayerAttribCsvDef* attrRadDef = GetDefManagerProxy()->getPlayerAttribCsvDef(PlayerAttributeType::Radiation);
	m_Radiation = attrRadDef->InitVal;
	m_MaxRadiation = attrRadDef->MaxVal;

	const PlayerAttribCsvDef* attrFoodDef = GetDefManagerProxy()->getPlayerAttribCsvDef(PlayerAttributeType::Food);
	for (int i = 0; i < PLAYERSTTYPE_MAX; i++) {
		m_FoodCostMap[i] = 0.f;
	}

	for (const auto& cost : attrFoodDef->ExternalDecreace) {
		m_FoodCostMap[cost.first] = cost.second.value;
	}

	const PlayerAttribCsvDef* attrThirstDef = GetDefManagerProxy()->getPlayerAttribCsvDef(PlayerAttributeType::Thirst);
	for (int i = 0; i < PLAYERSTTYPE_MAX; i++) {
		m_ThirstCostMap[i] = 0.f;
	}

	for (const auto& cost : attrThirstDef->ExternalDecreace) {
		m_ThirstCostMap[cost.first] = cost.second.value;
	}

	m_pDownedStateAttrib = SANDBOX_NEW(PlayerDownedStateAttrib, this, m_ClientPlayer);
	m_pDownedStateAttrib->initialize();

	// 旧属性，即将废弃 note by cloud.
	m_fBasicOverflowHP = lua_const->hpoverflow;
	m_Life = lua_const->hpmax;
	m_fBasicMaxStrength = lua_const->strengthmax;
	m_fMaxStrength = m_fBasicMaxStrength;
	m_fBasicOverflowStrength = lua_const->strengthoverflow;
	m_fOverflowStrength = m_fBasicOverflowStrength;
	m_fStrength = lua_const->strengthmax;
	m_fStrengthRestoreFactor = lua_const->default_tili_huifu_beilv;
	m_fBasicStrengthRestore = 1.f;
	m_fStrengthConsumptionOfChargingPerSecond = lua_const->strength_consumption_of_charging_per_second;
	m_bIsExhausted = false;
	m_fMaxStrengthForExhaustion = lua_const->max_strength_for_exhaustion;
	m_fMaxPercentageOfStrengthForExhaustion = lua_const->max_percentage_of_strength_for_exhaustion;
	m_maxOverDraw = lua_const->strength_max_overdraw;
	m_minOverDraw = lua_const->strength_min_overdraw;
	m_actor_revive_hp = lua_const->actor_revive_hp;
	m_actor_revive_strength = lua_const->actor_revive_strength;
	m_fThirstRestoreFactor = lua_const->default_tili_huifu_beilv;
	m_fBasicThirstRestore = 1.f;
	m_fThirstConsumptionOfChargingPerSecond = lua_const->thirst_consumption_of_charging_per_second;
	m_FoodLevel = lua_const->foodlevel;
	m_FoodSatLevel = lua_const->foodlevel;
	m_MaxFoodLvl = 100.0f;
	m_UsedStamina = 0;
	m_StillnessTick = 0;
	m_StrengthTick = 0;
	m_FoodAccumTick = 0;
	m_CanEatFood = true;
	m_ThirstTick = 0;

	m_CurShotcut = 0;
	m_CurShotcutEdit = 0;
	m_ItemAttMap.clear();
	m_bUseCompatibleStrength = true;
	if (m_bUseCompatibleStrength)
	{
		m_fRecover = lua_const->default_xieliang_huifu_beilv;
	}
	else
	{
		m_fRecover = lua_const->default_xieliang_huifu_beilv_old;
	}
	m_StrengthFoodShowState = SFS_Strength;

	// 等级经验
	setExp(0);
	m_pLevelMode = SANDBOX_NEW(PlayerLevelMode, this);
	m_pBaseAttrSetter = SANDBOX_NEW(PlayerBaseAttrSetter);

	if (m_ClientPlayer == g_pPlayerCtrl)
	{
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
	}

	m_isAttrShapeShift = false;
	m_oldBuffs.clear();
	m_oldStrength = 0;
	m_oldBasicMaxHP = 0;
	m_oldBasicMaxStrength = 0;
	m_oldLife = 0;
	m_oldFoodLevel = 0;
	m_AttrShapeShiftTick = 3;
	m_WaterPressureTick = 0;
	m_Lowerm_WaterPressureTick = 0;
	m_WaterDepth = 0;
	m_LowerWaterDepth = 0;
	m_LowerBodyWaterPressure = 0;
	m_bInitialized = true;
	m_NaturalTick = 0;
	m_StarDebuffTime = 0;
	m_StarDebuffStage = 0;
	m_chekckNeedConsumeStrength = false;
	m_chekckNeedConsumeThirst = false;
	ComputerOrderUsed.clear();

	m_isOffline = true;
	m_CurBuilldingId = 2;//墙
	//LOG_INFO("initialize(): end");
}

bool PlayerAttrib::isInitialized()
{
	return m_bInitialized;
}

PlayerAttrib::~PlayerAttrib()
{
	PlayerControl* ctrl =  dynamic_cast<PlayerControl*>(m_ClientPlayer);
	if (ctrl) {
		MINIW::ScriptVM* scriptvm = MINIW::ScriptVM::game();
		if (scriptvm) {
			scriptvm->setUserTypePointer("MainPlayerAttrib", "PlayerAttrib", NULL);
		}
	}
	m_ClientPlayer = NULL;

	SANDBOX_DELETE(m_pDownedStateAttrib);
	SANDBOX_DELETE(m_Backpack);
	SANDBOX_DELETE(m_CraftingQueue);
	SANDBOX_DELETE(m_pLevelMode);
	SANDBOX_DELETE(m_pBaseAttrSetter);
	DestroyEvent2();
}

void PlayerAttrib::CreateEvent2()
{
	typedef ListenerFunctionRef<bool&, int, bool> ListenerEatFood;
	m_listenerEatFood = SANDBOX_NEW(ListenerEatFood, [&](bool& enable, int foodid, bool inbackpack) -> void {
		if (!this->IsEnableEatFood())
		{
			enable = false;
			return;
		}
		enable = true;
		this->eatFood(foodid, inbackpack);
		});
	Event2().Subscribe("PlayerAttrib_EatFood", m_listenerEatFood);

	typedef ListenerFunctionRef<bool&, int, int, int> ListenerDrinkWater;
	m_listenerDrinkWater = SANDBOX_NEW(ListenerDrinkWater, [&](bool& enable, int waterVolume, int waterType, int itemid) -> void {
		if (!this->IsEnableEatFood()) // 复用饮食功能的开关
		{
			enable = false;
			return;
		}
		enable = true;
		this->drinkWater(waterVolume, waterType, itemid);
		});
	Event2().Subscribe("PlayerAttrib_DrinkWater", m_listenerDrinkWater);

	typedef ListenerFunctionRef<int&, int> Listener1;
	m_listenerPlayAttrib1 = SANDBOX_NEW(Listener1, [&](int& itemid, int slot) -> void {
		itemid = this->getEquipItem((EQUIP_SLOT_TYPE)slot);
		});
	Event2().Subscribe("PlayerAttrib_getEquipItem", m_listenerPlayAttrib1);

	typedef ListenerFunctionRef<int, int, int, World*> Listener2;
	m_listenerPlayAttrib2 = SANDBOX_NEW(Listener2, [&](int depth, int lowerDepth, int m_WaterPressureCoefficient, World* m_World) -> void {

		int curWaterDepth = this->m_WaterDepth;
		if (curWaterDepth <= 0)
		{
			this->m_WaterDepth = depth;
		}
		else
		{
			if (this->m_WaterDepth + 5 < depth)
				this->m_WaterDepth += 5;
			else if (this->m_WaterDepth - 5 > depth)
				this->m_WaterDepth -= 5;
			else
				this->m_WaterDepth = depth;
		}

		int curLowerDepth = this->m_LowerWaterDepth;
		if (curLowerDepth <= 0)
		{
			this->m_LowerWaterDepth = lowerDepth;
		}
		else
		{
			if (this->m_LowerWaterDepth + 5 < lowerDepth)
				this->m_LowerWaterDepth += 5;
			else if (this->m_LowerWaterDepth - 5 > lowerDepth)
				this->m_LowerWaterDepth -= 5;
			else
				this->m_LowerWaterDepth = lowerDepth;
		}
		// 水压等级=（（扫描高度-1）/换算系数+1）（不进位取整）,等价于 高度/系数 进位取整
		int press = (int)ceil(this->m_WaterDepth * 1.0f / m_WaterPressureCoefficient);
		int lowerPress = (int)ceil(this->m_LowerWaterDepth * 1.0f / m_WaterPressureCoefficient);
		if (press > 10) press = 10;
		if (lowerPress > 10) lowerPress = 10;
		if (!m_World->onServer())
		{
			if (this->getWaterPressure() != press || this->getLowerBodyWaterPressuer() != lowerPress)
			{
				jsonxx::Object context;
				context << "uin" << (GetIPlayerControl() ? GetIPlayerControl()->GetIUin() : 0);
				context << "waterPress" << press;
				context << "lowerWaterPress" << lowerPress;
				SandBoxManager::getSingleton().sendToHost("PB_WATER_PRESSURE_CH", context.bin(), context.binLen());
			}
		}
		this->setWaterPressure(press);
		this->setLowerBodyWaterPressuer(lowerPress);
		});
	Event2().Subscribe("PlayerAttrib_waterPress", m_listenerPlayAttrib2);

	typedef ListenerFunctionRef<float&> Listener3;
	m_listenerPlayAttrib3 = SANDBOX_NEW(Listener3, [&](float& fVal) -> void {
		if (this->isNewStatus()) 
		{
			fVal = this->getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_VIEW_BRIGHT, 1.0f) / 100.f;
		}
		else if (this->hasBuff(VIEW_BRIGHT_BUFF)) 
		{
			int buffIndex = this->getBuffIndex(VIEW_BRIGHT_BUFF);
			ActorBuff buffInfo = this->getBuffInfo(buffIndex);

			const BuffDef* def = GetDefManagerProxy()->getBuffDef(buffInfo.buffid, buffInfo.bufflv);
			if (def)
			{
				fVal = def->AttrValues[0];
			}
		}
		if (g_pPlayerCtrl->isOpenPierceViewBright())
		{
			fVal = Rainbow::Max(fVal, 0.6f);
		}
		});
	Event2().Subscribe("PlayerAttrib_worldvalue", m_listenerPlayAttrib3);

	typedef ListenerFunctionRef<int&> Listener4;
	m_listenerPlayAttrib4 = SANDBOX_NEW(Listener4, [&](int& depth) -> void {
		depth = this->m_WaterDepth;
		});
	Event2().Subscribe("PlayerAttrib_WaterDepth", m_listenerPlayAttrib4);

	typedef ListenerFunctionRef<float&> Listener5;
	m_listenerPlayAttrib5 = SANDBOX_NEW(Listener5, [&](float& farRange) -> void {
		float equipViewLight = this->getEquipViewLightBright();
		farRange += equipViewLight;
		});
	Event2().Subscribe("PlayerAttrib_farRange", m_listenerPlayAttrib5);

	typedef ListenerFunctionRef<int&> Listener6;
	m_listenerPlayAttrib6 = SANDBOX_NEW(Listener6, [&](int& shortcut) -> void {
		shortcut = this->getCurShotcut();
		});
	Event2().Subscribe("PlayerAttrib_getshortcut", m_listenerPlayAttrib6);

	typedef ListenerFunctionRef<PB_PlayerBriefInfo*> Listener7;
	Listener7* listener7 = SANDBOX_NEW(Listener7, [&](PB_PlayerBriefInfo* briefInfo) -> void {
		briefInfo->set_hp(this->getHP());
		briefInfo->set_maxhp(this->getMaxHP());
		briefInfo->set_overflowhp(this->getOverflowHP());
		briefInfo->set_strength(this->getStrength());
		briefInfo->set_maxstrength(this->getBasicMaxStrength());
		briefInfo->set_overflowstrength(this->getBasicOverflowStrength());
		briefInfo->set_armor(this->getArmor());
		briefInfo->set_perseverance(this->getPerseverance());
		});
	Event2().Subscribe("PlayerAttrib_setbriefInfo", listener7);

	typedef ListenerFunctionRef<float&, int&, int&> Listener8;
	m_listenerPlayAttrib8 = SANDBOX_NEW(Listener8, [&](float& mLastOxygen, int& mLastExp, int& mLastFoodLevel) -> void {
		mLastOxygen = this->getOxygen();
		mLastExp = this->getExp();
		mLastFoodLevel = this->getFoodLevel();
		});
	Event2().Subscribe("PlayerAttrib_InitValue", m_listenerPlayAttrib8);

	typedef ListenerFunctionRef<int&, bool&, float&, int&, IClientPlayer*, float, float> Listener9;
	m_listenerPlayAttrib9 = SANDBOX_NEW(Listener9, [&](int& mLastExp, bool& mHPSent, float& mLastOxygen, int& mLastFoodLevel, IClientPlayer* iplayer,float mLastHP, float mLastArmor) -> void {

		auto player = dynamic_cast<ClientPlayer*>(iplayer);
		if( player->getAttribDirty() || 
		mLastExp != this->getExp() || 
		mLastHP != this->getHP() || 
		mLastOxygen != this->getOxygen() || 
		mLastFoodLevel != this->getFoodLevel() || 
		mLastArmor != this->getArmor())
		{
			player->setAttribDirty(false);
			//LOG_INFO("MpActorTrackerEntry::checkPlayerChangeToSelf(): %d = %s", player->getUin(), this->toString().c_str());
			PB_PlayerAttrChangeHC playerAttrChangeHC;
			playerAttrChangeHC.set_exp(mLastExp = this->getExp());
			playerAttrChangeHC.set_maxhp(this->getMaxHP());
			playerAttrChangeHC.set_overflowhp(this->getOverflowHP());
			playerAttrChangeHC.set_hp(this->getHP());
			playerAttrChangeHC.set_maxstrength(this->getBasicMaxStrength());
			playerAttrChangeHC.set_overflowstrength(this->getBasicOverflowStrength());
			playerAttrChangeHC.set_strength(this->getStrength());
			playerAttrChangeHC.set_oxygen(mLastOxygen = this->getOxygen());
			playerAttrChangeHC.set_foodlevel(mLastFoodLevel = this->getFoodLevel());
			playerAttrChangeHC.set_armor(this->getArmor());
			playerAttrChangeHC.set_perseverance(this->getPerseverance());
			//playerAttrChangeHC.set_foodvalue(m_FoodValue->GetValue());
			//playerAttrChangeHC.set_thirstvalue(m_ThirstValue->GetThirstValue());

			if (this->getHP() < 0)
			{
				PB_DieInfo* dieinfo = playerAttrChangeHC.mutable_dieinfo();
				const SocAttackInfo& attackinfo = player->getSocAttackInfo();

				dieinfo->set_atktype(attackinfo.atktype);
				dieinfo->set_buffid(attackinfo.buffId);
				dieinfo->set_bufflevel(attackinfo.buffLevel);
				dieinfo->set_toolid(attackinfo.toolid);
				dieinfo->set_playerid(attackinfo.playerid);
				dieinfo->set_length(attackinfo.length);
				dieinfo->set_mobid(attackinfo.mobid);
				dieinfo->set_survival_time(player->getSurvivalTime());
			}

			GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_PLAYER_ATTR_CHANGE_HC, playerAttrChangeHC);

			if (player->getSpectatorUin())
			{
				if (g_pPlayerCtrl && (g_pPlayerCtrl->getUin() == player->getSpectatorUin()))
				{
					//ge GetGameEventQue().postPlayerAttrChange();
					MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
					if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
						MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
				}
				else
				{
					GetGameNetManagerPtr()->sendToClient(player->getSpectatorUin(), PB_OTHER_PLAYER_ATTR_CHANGE_HC, playerAttrChangeHC);
				}
			}

			mLastExp = this->getExp();
			mHPSent = true;
			mLastOxygen = this->getOxygen();
			mLastFoodLevel = this->getFoodLevel();
		}
		});
	Event2().Subscribe("PlayerAttrib_UpdateAttrib", m_listenerPlayAttrib9);
}

void PlayerAttrib::DestroyEvent2()
{
	SANDBOX_RELEASE(m_listenerEatFood);
	SANDBOX_RELEASE(m_listenerDrinkWater);
	SANDBOX_RELEASE(m_listenerPlayAttrib1);
	SANDBOX_RELEASE(m_listenerPlayAttrib2);
	SANDBOX_RELEASE(m_listenerPlayAttrib3);
	SANDBOX_RELEASE(m_listenerPlayAttrib4);
	SANDBOX_RELEASE(m_listenerPlayAttrib5);
	SANDBOX_RELEASE(m_listenerPlayAttrib6);
	SANDBOX_RELEASE(m_listenerPlayAttrib8);
	SANDBOX_RELEASE(m_listenerPlayAttrib9);
}

void PlayerAttrib::revive()
{
	LivingAttrib::revive();

	m_FoodValue->SetFoodValue(GetDefManagerProxy()->getPlayerAttribCsvDef(PlayerAttributeType::Food)->ReviveVal);
	m_ThirstValue->SetThirstValue(GetDefManagerProxy()->getPlayerAttribCsvDef(PlayerAttributeType::Thirst)->ReviveVal);
	m_Temperature = GetDefManagerProxy()->getPlayerAttribCsvDef(PlayerAttributeType::Temperature)->ReviveVal;
	m_FinalPosTemperature = m_Temperature;
	m_Radiation = GetDefManagerProxy()->getPlayerAttribCsvDef(PlayerAttributeType::Radiation)->ReviveVal;
	// 旧属性，即将删除 note by cloud.
	m_Life = 100;
	m_FoodLevel = 100; 
	m_fStrength = 100;
	m_FoodSatLevel = 0;
	m_UsedStamina = 0;
	m_FoodAccumTick = 0;
	m_StillnessTick = 0;
	m_StrengthTick = 0;

	m_ThirstTick = 0;

	if (GetLuaInterfaceProxy().shouldUseNewHpRule())
	{
		setArmor(0);
		setPerseverance(0);
	}
	if (m_ClientPlayer == g_pPlayerCtrl)
	{
		//ge GetGameEventQue().postPlayerAttrChange();
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
	}
	// Force the player into normal state when reviving
	if (m_pDownedStateAttrib)
	{
		m_pDownedStateAttrib->exitDownedState();
	}
}

void PlayerAttrib::clearrevive(int cleartype)
{
	LivingAttrib::clearrevive(cleartype);

	if (cleartype == 1 && m_Backpack)
		m_Backpack->clearPack("clearrevive");
}


int PlayerAttrib::getCalculatedWaterPressure()
{
	if (g_WorldMgr && !g_WorldMgr->canWaterPressAffectPlayer())
	{
		return 0;
	}
	int equipbreast = getEquipItemWithType(EQUIP_BREAST);
	//潜水服，和高级潜水服 会消除水压值
	if (equipbreast == ITEM_DIVING_SUIT || equipbreast == ITEM_DIVING_SUIT_SUPER)
	{
		return 0;
	}

	return LivingAttrib::getCalculatedWaterPressure();
}

float PlayerAttrib::getMoveBaseSpeed(int type)
{
	float speed = VELOCITY_FB;
	if (type >= Actor_Walk_Speed && type <= Actor_Jump_Speed) {
		if (m_fSpeed[type] >= 0) {
			speed = m_fSpeed[type];
		}
	}

	return speed;
}


int PlayerAttrib::getEquipAddSpeed(int type)
{
	ClientPlayer* player = m_ClientPlayer;
	if (player == NULL) return 0.f;
	int addspeed = 0;
	for (int i = 0; i < EQUIP_WEAPON; i++)
	{
		BackPackGrid* grid = getEquipGrid((EQUIP_SLOT_TYPE)i);
		if (grid && grid->def && grid->getDuration() > 0)
		{
			const ToolDef* tool = GetDefManagerProxy()->getToolDef(grid->def->ID);
			if (tool)
			{
				addspeed += tool->MoveSpeed;
				if (type == Actor_Swim_Speed)
				{
					addspeed += tool->SwimSpeed;
				}
			}
		}
	}
	if (addspeed < -100) addspeed = -100;


	return addspeed;
}

float PlayerAttrib::calculateMoveSpeed(float speed, int type)
{
	ClientPlayer *player = m_ClientPlayer;
	if (player == NULL) return 0.f;
	// 检查玩家是否处于倒地状态
	if (m_pDownedStateAttrib && !m_pDownedStateAttrib->canMove())
	{
		float speed = 3.0f;
		const SocAttackInfo& attackinfo = m_ClientPlayer->getSocAttackInfo();
		const DieInfoCsvDef* dieinfodef = GetDefManagerProxy()->getDieinfoCsvDef((int)(attackinfo.atktype));
		if (!dieinfodef)
		{
			return speed;
		}
		//LOG_WARNING("dieinfo move = %d", dieinfodef->move);

		speed = dieinfodef->move == 0 ? .0f : 3.0f;

		return speed;  // 倒地状态下禁止移动
	}

	int addspeed = getEquipAddSpeed(type);

	//float speed = VELOCITY_FB;
	//if (type >= Actor_Walk_Speed && type <= Actor_Jump_Speed) {
	//	if (m_fSpeed[type] >= 0) {
	//		speed = m_fSpeed[type];
	//	}
	//}

	//玩法规则设置: 移动速度
	//if (GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->getBaseSettingManager())
	//	speed = m_pBaseAttrSetter->m_nMoveSpeed;

	//推着雪球时
	auto* ball = player->getCatchBall();
	if (ball)
	{
		ActorPushSnowBall* snowball = dynamic_cast<ActorPushSnowBall*>(ball);
		if (snowball)
		{
			PushSnowBallLocomotion* pLoco = dynamic_cast<PushSnowBallLocomotion*>(snowball->getLocoMotion());
			if (pLoco)
			{
				speed = pLoco->getSnowBallSpeed();
			}
		}
	}

	auto CarryComp = player->getCarryComponent();
	if (CarryComp && CarryComp->isCarrying())
	{
		speed *= 0.7f;		//扛着生物时，基础速度下降到正常的70%；
	}

	speed = speed * (1.0f + player->getGeniusValue(GENIUS_MOVESPEED_INC)) * (1.0f + addspeed / 100.0f) * (1.0f + player->getSpecSpeed() / 100.0f);
#if 0
	if (isNewStatus()) {
		float tmp = speed;
		speed += getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_WALK_SPEED, tmp);

		if (type >= Actor_Run_Speed && type <= Actor_Swim_Speed) {
			int iAttType = BuffAttrType::BUFFATTRT_RUN_SPEED;
			if (type == Actor_Sneak_Speed)
				iAttType = BuffAttrType::BUFFATTRT_SNEAK_SPEED;
			else if (type == Actor_Swim_Speed)
				iAttType = BuffAttrType::BUFFATTRT_SWIM_SPEED;

			tmp = speed;
			speed += getActorAttValueWithStatus(iAttType, tmp);
		}
	}
	else {
		speed = speed * (1.0f + getModAttrib(MODATTR_MOVE_SPEED));
	}
#else
	float tmp = speed;
	speed += getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_WALK_SPEED, tmp);

	if (type >= Actor_Run_Speed && type <= Actor_Swim_Speed) {
		int iAttType = BuffAttrType::BUFFATTRT_RUN_SPEED;
		if (type == Actor_Sneak_Speed)
			iAttType = BuffAttrType::BUFFATTRT_SNEAK_SPEED;
		else if (type == Actor_Swim_Speed)
			iAttType = BuffAttrType::BUFFATTRT_SWIM_SPEED;

		tmp = speed;
		speed += getActorAttValueWithStatus(iAttType, tmp);
	}
#endif

	if (speed < 0.0f) { speed = 0.0f; }

	return speed;
	//return speed * (1.0f + fBuffSpeed) * (1.0f + player->getGeniusValue(GENIUS_MOVESPEED_INC)) * (1.0f + addspeed/100.0f) * (1.0f + player->getSpecSpeed()/100.0f);
}


float PlayerAttrib::getMoveSpeed(int type /* = 0 */)
{
	float baseSpeed = getMoveBaseSpeed(type);
	float speed = calculateMoveSpeed(baseSpeed, type);

	return speed;
	//return speed * (1.0f + fBuffSpeed) * (1.0f + player->getGeniusValue(GENIUS_MOVESPEED_INC)) * (1.0f + addspeed/100.0f) * (1.0f + player->getSpecSpeed()/100.0f);
}

void PlayerAttrib::applyEquips(ActorBody* body, EQUIP_SLOT_TYPE t, bool takeoffAble)
{
	if (body)
	{
		body->setTakeoffAble(takeoffAble);
		auto realEquipItem = [this](ActorBody* body, int idx) -> void
			{
				if ((EQUIP_SLOT_TYPE)idx == EQUIP_WEAPON)
				{
					body->setEquipItem(EQUIP_WEAPON, getEquipItem(EQUIP_WEAPON));
					return;
				}
				auto itemid = getEquipItem((EQUIP_SLOT_TYPE)idx);
				if (itemid > 0)
				{
					const auto tooldef = GetDefManagerProxy()->getToolDef(itemid);
					if (tooldef)
					{
						auto equipType = tooldef->getSlotType();
						if (equipType != EQUIP_NONE)
						{
							//先进行卸载 对数据位处理 对同位对换处理
							//分件组合装备卸载不需要进行属性处理，因为是先调一次卸载，再调一次装备
							auto it = m_EquipPos2Type.find((EQUIP_BACK_INDEX)idx);
							if (it != m_EquipPos2Type.end())
							{
								auto lastgroupType = it->second.getGroupType();
								auto lastequipType = (EQUIP_SLOT_TYPE)it->second.getSourceTypeORIdx();
								if (lastgroupType != 1)
								{
									body->setEquipItem(lastequipType, 0);
									m_EquipPos2Type.erase(it);
									m_EquipType2Pos.erase(lastequipType);
								}
								else//组合装备装备特殊处理
								{
									auto lastequidId = it->second.getSourceItemId();
									const auto lastdef = GetDefManagerProxy()->getEquipGroupDef(lastequidId);
									if (lastdef)
									{
										for (const auto& item : lastdef->GroupEquipMap)
										{
											auto lastsourceIdx = m_EquipType2Pos[item.first].getSourceTypeORIdx();
											if (lastsourceIdx == idx)
											{
												body->setEquipItem(item.first, 0);
												m_EquipType2Pos.erase(item.first);
											}
										}
									}
									m_EquipPos2Type.erase(it);
								}
							}
							//进行穿戴
							if (equipType == EQUIP_GROUP)
							{
								//新装备的是组合装备，读取新增的组合装备配置  todo by charles xie
								auto groupDef = GetDefManagerProxy()->getEquipGroupDef(itemid);
								if (groupDef)
								{
									//先卸载 判断组合中的某个类型装备是否已经穿戴
									for (auto& item : groupDef->GroupEquipMap)
									{
										auto it = m_EquipType2Pos.find(item.first);
										if (it != m_EquipType2Pos.end())
										{
											//替换的装备放回背包需要clientplayer对象。即只有角色可以有多背包和丢弃操作
											if (m_OwnerActor)
											{
												ClientPlayer* ownerPlayer = static_cast<ClientPlayer*>(m_OwnerActor);
												if (ownerPlayer && ownerPlayer->getWorld() && ownerPlayer->getWorld()->onServer())
												{
													//不管是否组合装备，只要获取对应的数据位grid，卸载数据 跑一边卸载流程
													auto lastsourceIdx = it->second.getSourceTypeORIdx();
													auto lastGrid = getEquipGrid((EQUIP_SLOT_TYPE)lastsourceIdx);
													if (lastGrid && !lastGrid->isEmpty())
													{
														GridCopyData gridcopydata(lastGrid);
														gridcopydata.gridix = -1;
														//有装备数据，把装备数据放回背包
														int intakeCount = m_Backpack->addItem_byGridCopyData(gridcopydata, 2);
														if (intakeCount == 0)
														{
															//背包满了，丢弃
															ownerPlayer->throwItem(gridcopydata.resid, 1);
														}
														int lastGridIndex = lastGrid->getIndex();
														lastGrid->clear();
														m_Backpack->afterChangeGrid(lastGridIndex);
														ownerPlayer->onDisApplyEquips(lastGridIndex, 0, gridcopydata.resid, 1);
														//不用处理卸载流程，因为卸载流程在穿戴流程中已经处理了
													}
												}
											}
										}
									}
									//穿戴
									for (auto& item : groupDef->GroupEquipMap)
									{
										EquipPosTypeChangeData t2pData;
										t2pData.setGroupData(1, idx, item.second);
										m_EquipType2Pos[item.first] = t2pData;
										body->setEquipItem(item.first, item.second);
									}
									EquipPosTypeChangeData p2tData;
									p2tData.setGroupData(1, equipType, itemid);
									m_EquipPos2Type[(EQUIP_BACK_INDEX)idx] = p2tData;
								}
							}
							else
							{
								//先判断卸载（按类型）
								auto equipTypeIter = m_EquipType2Pos.find(equipType);
								if (equipTypeIter != m_EquipType2Pos.end())
								{
									if (m_OwnerActor)
									{
										ClientPlayer* ownerPlayer = static_cast<ClientPlayer*>(m_OwnerActor);
										if (ownerPlayer && ownerPlayer->getWorld() && ownerPlayer->getWorld()->onServer())
										{
											auto lastGroupType = equipTypeIter->second.getGroupType();
											auto lastEquipIdx = equipTypeIter->second.getSourceTypeORIdx();
											auto lastEquipGrid = getEquipGrid((EQUIP_SLOT_TYPE)lastEquipIdx);
											if (lastEquipGrid && !lastEquipGrid->isEmpty())
											{
												GridCopyData gridcopydata(lastEquipGrid);
												gridcopydata.gridix = -1;
												int intakeCount = m_Backpack->addItem_byGridCopyData(gridcopydata, 2);
												if (intakeCount == 0)
												{
													//背包满了，丢弃
													ownerPlayer->throwItem(gridcopydata.resid, 1);
												}
												int lastGridIndex = lastEquipGrid->getIndex();
												lastEquipGrid->clear();
												m_Backpack->afterChangeGrid(lastGridIndex);
												ownerPlayer->onDisApplyEquips(lastGridIndex, 0, gridcopydata.resid, 1);
											}
										}
									}
								}
								EquipPosTypeChangeData p2tData;
								EquipPosTypeChangeData t2pData;
								auto groupType = tooldef->SubType == 15 ? 2 : 0;
								p2tData.setGroupData(groupType, equipType, itemid);
								t2pData.setGroupData(groupType, idx, itemid);
								m_EquipPos2Type[(EQUIP_BACK_INDEX)idx] = p2tData;
								m_EquipType2Pos[equipType] = t2pData;
								body->setEquipItem(equipType, itemid);
							}
						}
					}
				}
				else
				{
					auto it = m_EquipPos2Type.find((EQUIP_BACK_INDEX)idx);
					if (it != m_EquipPos2Type.end())
					{
						auto equipType = (EQUIP_SLOT_TYPE)it->second.getSourceTypeORIdx();
						int groupType = it->second.getGroupType();
						if (groupType != 1)
						{
							body->setEquipItem(equipType, 0);
							m_EquipType2Pos.erase(equipType);
							if (groupType == 2)
							{
								//解除分件组合属性
							}
						}
						else//组合装备卸载特殊处理
						{
							auto equidId = it->second.getSourceItemId();
							const auto def = GetDefManagerProxy()->getEquipGroupDef(equidId);
							if (def)
							{
								for (const auto& item : def->GroupEquipMap)
								{
									auto sourceIdx = m_EquipType2Pos[item.first].getSourceTypeORIdx();
									if (sourceIdx == idx/* && equidId == m_EquipType2Pos[item.first].getSourceItemId()*/)
									{
										body->setEquipItem(item.first, 0);
										m_EquipType2Pos.erase(item.first);
									}
								}
							}
						}
						m_EquipPos2Type.erase(it);
					}
				}
			};
		OPTICK_EVENT();
		if (t == MAX_EQUIP_SLOTS)
		{
			for (int i = 0; i < EQUIP_BACK_INDEXMAX; i++)
			{
				realEquipItem(body, i);
			}
		}
		else
		{
			realEquipItem(body, t);
		}
	}
}

void PlayerAttrib::equip(EQUIP_SLOT_TYPE t, BackPackGrid* itemgrid)
{
	if (!itemgrid)
		equip(t, 0, -1, 0, 0);
	else
		equip(t, itemgrid->getItemID(), itemgrid->getDuration(), itemgrid->getToughness(), itemgrid->getMaxDuration());
}

void PlayerAttrib::equip(EQUIP_SLOT_TYPE t, int itemid, int durable, int toughness, int maxdurable)
{
	BackPackGrid *grid = getEquipGrid(t);

	DefDataTable<ToolDef> &toolTable = GetDefManagerProxy()->getToolTable();
	ToolDef *tool = toolTable.GetRecord(itemid);
	if (tool)
	{
		//if (durable < 0) durable = tool->Duration;
		SetBackPackGrid(*grid, itemid, 1, durable, toughness);
		grid->setMaxDuration(maxdurable);
	}
	else
	{
		SetBackPackGrid(*grid, 0, 0);
	}

	static_cast<ClientPlayer *>(m_OwnerActor)->applyEquips(t);
}

int PlayerAttrib::getEquipItem(EQUIP_SLOT_TYPE t)
{
	BackPackGrid *pgrid = getEquipGrid(t);
	if (pgrid && pgrid->def) return pgrid->def->ID;
	else return 0;
}

int PlayerAttrib::getEquipItemWithType(EQUIP_SLOT_TYPE t)
{
	if (t == EQUIP_WEAPON)
	{
		BackPackGrid* pgrid = getEquipGrid(t);
		if (pgrid && pgrid->def) return pgrid->def->ID;
		else return 0;
	}

	BackPackGrid* pgrid = getEquipGridWithType(t);
	if (pgrid && pgrid->def) return pgrid->def->ID;
	return 0;
}

int PlayerAttrib::equipSlot2Index(EQUIP_SLOT_TYPE t)
{
	if (t == EQUIP_WEAPON) return m_Backpack->getShortcutStartIndex() + getCurShotcut();
	else return EQUIP_START_INDEX + t;
}


int PlayerAttrib::getCurShotcut()
{
	if (g_WorldMgr)
	{
		if (g_WorldMgr->isUGCEditMode())
		{
			return 0;
		}
		else if (g_WorldMgr->isUGCEditBuildMode())
		{
			if (m_Backpack->isTmpShortcutMode())
			{
				return m_CurShotcut;
			}
			return m_CurShotcutEdit;
		}
	}
	return m_CurShotcut;
}

void PlayerAttrib::setCurShotcut(int index)
{
	if (g_WorldMgr)
	{
		if (g_WorldMgr->isUGCEditMode())
		{
			return;
		}
		else if (g_WorldMgr->isUGCEditBuildMode() && !m_Backpack->isTmpShortcutMode())
		{
			m_CurShotcutEdit = index;
			return;
		}
	}
	//这里做容错处理，其他模式index设置成>7的情况
	if(index<0 || index>7)
	{
		index = 0;
	}
	m_CurShotcut = index;
}

BackPackGrid *PlayerAttrib::getEquipGrid(EQUIP_SLOT_TYPE t)
{
	BackPackGrid* grid = m_Backpack->index2Grid(equipSlot2Index(t));
	return grid;
}

BackPackGrid* PlayerAttrib::getEquipGridWithType(EQUIP_SLOT_TYPE t)
{
	if (t == EQUIP_WEAPON)
	{
		BackPackGrid* grid = m_Backpack->index2Grid(equipSlot2Index(t));
		return grid;
	}
	else
	{
		auto idx = getEquipIdxByType(t);
		if (idx != EQUIP_BACK_INDEX_NONE)
		{
			BackPackGrid* grid = getEquipGrid((EQUIP_SLOT_TYPE)idx);
			return grid;
		}
		//for (int i = 0; i < EQUIP_WEAPON; i++)
		//{
		//	BackPackGrid* grid = getEquipGrid((EQUIP_SLOT_TYPE)i);
		//	if (grid && grid->def)
		//	{
		//		const auto tooldef = GetDefManagerProxy()->getToolDef(grid->def->ID);
		//		if (tooldef)
		//		{
		//			auto equipType = tooldef->getSlotType();
		//			if (equipType != MAX_EQUIP_SLOTS)
		//			{
		//				if (equipType == t)
		//				{
		//					return grid;
		//				}
		//			}
		//			// else if (tooldef->Type == 39)
		//			// {
		//			// 	//读取新增的组合装备配置  todo by charles xie
		//			// }
		//		}
		//	}
		//}
	}
	return NULL;
}

void PlayerAttrib::onCurToolUsed(int num, bool ignore_durable, bool needAutoAdd)
{
	int index = m_Backpack->getShortcutStartIndex() + getCurShotcut();

	BackPackGrid *grid = m_Backpack->index2Grid(index);
	if (grid->def == NULL) return;

	int itemId = grid->def->ID;

	bool isHomeland = (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD);
	const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(itemId);

	//对于不可堆叠的的工具类，消耗耐久
	if (!ignore_durable && grid->def->StackMax <= 1 && tooldef && tooldef->Duration > 0)
	{
		if (GetWorldManagerPtr() && (!GetWorldManagerPtr()->isGodMode() || isHomeland) && grid->addDuration(num, true) <= 0)
		{

			auto sound = m_OwnerActor->getSoundComponent();
			if (sound)
			{
				sound->playSound("misc.break", 1.0f, 1.0f);
			}

			// 耐久掉完之后 不摧毁物品
			m_Backpack->afterChangeGrid(index);

			// ClientPlayer *player = static_cast<ClientPlayer *>(m_OwnerActor);
			// if (tooldef && player)
			// 	player->addOWScore(tooldef->Score);
			//
			// if (player)
			// {
			// 	player->consumeItemOnTrigger(itemId, 1);
			// 	// 道具被消耗
			// 	ObserverEvent_ActorItem obevent(player->getObjId(), itemId, 1);
			// 	GetObserverEventManager().OnTriggerEvent("Item.Damage", &obevent);
			// }
		}
		else m_Backpack->afterChangeGrid(index);
	}
	else
	{
		if (isHomeland)
		{
			//家园地图 放置一个方块 就在背包里面扣减掉一个方块
			MINIW::ScriptVM::game()->callFunction("CostBackpackItemInHomeLand", "ii", itemId, 1);
		}
		else
		{
			m_Backpack->removeItem(index, 1);
		}

		ClientPlayer *player = static_cast<ClientPlayer *>(m_OwnerActor);
		if (player)
		{
			player->consumeItemOnTrigger(itemId, 1);
		}
	}
	if (needAutoAdd)
	{
		autoAddCurShortcutItem(itemId);
	}

	//	MINIW::ScriptVM::game()->callFunction("AutoAddCurShortCutItem", "i", itemId);
}

void PlayerAttrib::onDrinkWaterBug(int drinkType)
{
	int index = m_Backpack->getShortcutStartIndex() + getCurShotcut();
	BackPackGrid* waterbug = m_Backpack->index2Grid(index);
	if (waterbug->def == NULL || waterbug->def->Type != 100) return;
	int itemId = waterbug->def->ID;
	const FoodDef* def = GetDefManagerProxy()->getFoodDef(itemId);
	int amount = 0;
	if (def)
	{
		amount = def->HealThirst * 10;
	}
	int curValue = waterbug->getWaterVolume();
	amount = std::min(curValue, amount);
	if (amount > 0)
	{
		waterbug->addWaterVolume(-amount);
		drinkWater(amount, drinkType, itemId);
		m_Backpack->afterChangeGrid(index);
	}
}

void PlayerAttrib::autoAddCurShortcutItem(int itemid)
{
	int curshortidx = m_Backpack->getShortcutStartIndex() + getCurShotcut();
	int curId = m_Backpack->getGridItem(curshortidx);
	if (curId > 0)
		return;

	for (int i = 0; i < 8; i++)
	{
		int grid_index = m_Backpack->getShortcutStartIndex() + i;
		if (itemid == m_Backpack->getGridItem(grid_index))
		{
			m_Backpack->swapItem(grid_index, curshortidx);
			return;
		}
	}
	for (int i = 0; i < 30; i++)
	{
		int grid_index = BACKPACK_START_INDEX + i;
		if (itemid == m_Backpack->getGridItem(grid_index))
		{
			m_Backpack->swapItem(grid_index, curshortidx);
			break;
		}
	}
}

int PlayerAttrib::getEquipItemDuration(EQUIP_SLOT_TYPE t)
{
	BackPackGrid *grid = getEquipGrid(t);
	if (grid->def == NULL) return 0;

	const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(grid->def->ID);
	if (tooldef == NULL || tooldef->Duration == 0) return 0;

	ClientPlayer *player = static_cast<ClientPlayer *>(m_OwnerActor);

	return grid->getDuration();
}


int PlayerAttrib::getEquipItemMaxDuration(EQUIP_SLOT_TYPE t)
{
	BackPackGrid* grid = getEquipGridWithType(t);
	if (grid == NULL || grid->def == NULL) return 0;

	const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(grid->def->ID);
	if (tooldef == NULL || tooldef->Duration == 0) return 0;

	int cur = grid->getDuration();

	return grid->getMaxDuration();
}

float PlayerAttrib::getEquipViewLightBright()
{
	int addViewLight = 0;
	for (int i = 0; i <= EQUIP_WEAPON; i++)
	{
		auto slotType = (EQUIP_SLOT_TYPE)i;
		BackPackGrid* grid = getEquipGrid(slotType);
		if (grid && grid->def)
		{
			const ToolDef* tool = GetDefManagerProxy()->getToolDef(grid->def->ID);
			if (tool)
			{
				auto toolSlotType = tool->getSlotType();
				//只有工具的装备类型和当前装备槽匹配、或者是普通非装备道具拿在手上才生效
				if (toolSlotType == slotType || (slotType == EQUIP_WEAPON && toolSlotType == MAX_EQUIP_SLOTS))
				{
					addViewLight += tool->ViewLight;
				}
			}
		}
	}

	return addViewLight;
}

int PlayerAttrib::damageEquipItem(EQUIP_SLOT_TYPE t, int damage)
{
	BackPackGrid *grid = getEquipGrid(t);
	if (grid->def == NULL) return 0;//返回0装备栏没东西

	const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(grid->def->ID);
	if (tooldef == NULL || tooldef->Duration == 0) return -2;//-1为不是道具

	ClientPlayer *player = static_cast<ClientPlayer *>(m_OwnerActor);

	if (GetWorldManagerPtr()->isGodMode() && (grid->def->ID != ITEM_FIRE_ROCKET))
	{
		return -1;
	}
	if (grid->getDuration() <= 0)
		return -1;

	if (grid->addDuration(-damage) <= 0)
	{
		if (grid->def->ID == ITEM_JETPACK)
		{
			auto sound = m_OwnerActor->getSoundComponent();
			if (sound)
			{
				sound->playSound("misc.jetpack_5", 1.0f, 1.0f);
			}
		}
		else if (grid->def->ID == ITEM_FIRE_ROCKET)
		{
			//爆炸
			player->getWorld()->getEffectMgr()->playParticleEffectAsync("particles/Chupinazoboom.ent", player->getLocoMotion()->getPosition(), 50, 0, 0, true);

			auto functionWrapper = player->getFuncWrapper();
			if (functionWrapper)
			{
				functionWrapper->setImmuneFall(1);
			}

			auto sound = player->getSoundComponent();
			if (sound)
			{
				sound->playSound("item.12822.blast", 1.0f, 1.0f, 3);
			}
		}
		else
		{
			auto sound = m_OwnerActor->getSoundComponent();
			if (sound)
			{
				sound->playSound("misc.equipment_break", 1.0f, 1.0f);
			}
		}

		int  itemid = grid->def->ID;
		player->addAchievement(1, ACHIEVEMENT_WEAROUT, itemid);
		player->updateTaskSysProcess(TASKSYS_WEAROUT_ITEM, itemid);
		if (grid->getDuration() == 0)
			player->addOWScore(tooldef->Score);
		bool canRemove = false;
		auto runeItem = grid->getRuneData();
		if (!grid->haveEnchant(ENCHANT_DURABLE_PROTECT))
			canRemove = true;
		if (canRemove)
			equip(t, nullptr);
		if (player->getWorld()->getCurMapID() == MAPID_LIEYANSTAR)
		{
			if (itemid == ITEM_FIRESAFETY_PACK) //防火服
			{
				removeBuff(HOT_BUFF, false);
			}
		}
		// 道具被消耗
		if (canRemove)
		{
			ObserverEvent_ActorItem obevent(m_OwnerActor->getObjId(), itemid, 1);
			GetObserverEventManager().OnTriggerEvent("Item.Damage", &obevent);
		}

		player->getBackPack()->afterChangeGrid(equipSlot2Index(t));
		return canRemove ? itemid : 0;
	}

	player->getBackPack()->afterChangeGrid(equipSlot2Index(t));
	return 0;
}

int PlayerAttrib::damageEquipItemWithType(EQUIP_SLOT_TYPE t, int damage)
{
	BackPackGrid* grid = NULL;
	const ToolDef* tooldef = NULL;
	if (t == EQUIP_WEAPON)
	{
		grid = getEquipGrid(t);
		if (grid && grid->def)
		{
			tooldef = GetDefManagerProxy()->getToolDef(grid->def->ID);
		}
	}
	else
	{
		auto equipIdx = m_EquipType2Pos.find(t);
		if (equipIdx != m_EquipType2Pos.end())
		{
			grid = getEquipGrid((EQUIP_SLOT_TYPE)equipIdx->second.getSourceTypeORIdx());
			if (grid && grid->def)
			{
				tooldef = GetDefManagerProxy()->getToolDef(grid->def->ID);
			}
		}	
	}

	if (grid == NULL || grid->def == NULL) return -2;//-2为装备栏没有东西

	if (tooldef == NULL || tooldef->Duration == 0) return -1;//-1为不是道具或无效了

	ClientPlayer* player = static_cast<ClientPlayer*>(m_OwnerActor);

	if (GetWorldManagerPtr()->isGodMode() && (grid->def->ID != ITEM_FIRE_ROCKET))
	{
		return -1;
	}
	if (grid->getDuration() <= 0)
		return -1;

	if (grid->addDuration(-damage) <= 0)
	{
		if (grid->def->ID == ITEM_JETPACK)
		{
			auto sound = m_OwnerActor->getSoundComponent();
			if (sound)
			{
				sound->playSound("misc.jetpack_5", 1.0f, 1.0f);
			}
		}
		else if (grid->def->ID == ITEM_FIRE_ROCKET)
		{
			//爆炸
			player->getWorld()->getEffectMgr()->playParticleEffectAsync("particles/Chupinazoboom.ent", player->getLocoMotion()->getPosition(), 50, 0, 0, true);

			auto functionWrapper = player->getFuncWrapper();
			if (functionWrapper)
			{
				functionWrapper->setImmuneFall(1);
			}

			auto sound = player->getSoundComponent();
			if (sound)
			{
				sound->playSound("item.12822.blast", 1.0f, 1.0f, 3);
			}
		}
		else
		{
			auto sound = m_OwnerActor->getSoundComponent();
			if (sound)
			{
				sound->playSound("misc.equipment_break", 1.0f, 1.0f);
			}
		}

		int  itemid = grid->def->ID;
		player->addAchievement(1, ACHIEVEMENT_WEAROUT, itemid);
		player->updateTaskSysProcess(TASKSYS_WEAROUT_ITEM, itemid);
		if (grid->getDuration() == 0)
			player->addOWScore(tooldef->Score);
		bool canRemove = false;
		auto runeItem = grid->getRuneData();
		if (!grid->haveEnchant(ENCHANT_DURABLE_PROTECT))
			canRemove = true;
		if (canRemove)
			equip(t, nullptr);
		if (player->getWorld()->getCurMapID() == MAPID_LIEYANSTAR)
		{
			if (itemid == ITEM_FIRESAFETY_PACK) //防火服
			{
				removeBuff(HOT_BUFF, false);
			}
		}
		// 道具被消耗
		if (canRemove)
		{
			ObserverEvent_ActorItem obevent(m_OwnerActor->getObjId(), itemid, 1);
			GetObserverEventManager().OnTriggerEvent("Item.Damage", &obevent);

			//GetAdventureReportMgrProxy()->consumeItem(0, itemid, 1, player->getUin());
		}

		player->getBackPack()->afterChangeGrid(grid->getIndex());
		return canRemove ? itemid : 0;
	}

	player->getBackPack()->afterChangeGrid(grid->getIndex());
	return 0;//0为没被破坏
}

void PlayerAttrib::dropEquipItems()
{
	int dropcount = 9;   // 应该变成玩家的属性  可根据等级或者保护等级调整
	int indices[] = { m_Backpack->getShortcutStartIndex(), BACKPACK_START_INDEX, EQUIP_START_INDEX ,WITHHOLD_BACKPACK_START_INDEX };
	auto dropComponent = m_OwnerActor->GetComponent<DropItemComponent>();
	for (int i = 0; i < 3; i++)
	{
		PackContainer *pack = (PackContainer *)m_Backpack->getContainer(indices[i]);

		for (size_t n = 0; n < pack->m_Grids.size(); n++)
		{
			auto& grid = pack->m_Grids[n];

			if (!grid.isEmpty())
			{
				if (checkIfItemHasAttAction(grid.getItemID(), PlayerItemAttType::Disable_Drop))
					continue;
				if (dropcount-- <= 0)
					break;

				if (dropComponent)
				{
					dropComponent->dropItem(&grid);
				}
				grid.clear();
				pack->afterChangeGrid(grid.getIndex());
			}
		}
	}

	//m_Backpack->clearPack();
}

void PlayerAttrib::dropEquipItemsToChest(int itemid)
{
	int indices[] = { m_Backpack->getShortcutStartIndex(), BACKPACK_START_INDEX, EQUIP_START_INDEX };
	WCoord blockpos = CoordDivBlock(m_OwnerActor->getPosition());
	m_OwnerActor->getWorld()->setBlockAll(blockpos, itemid, m_OwnerActor->getCurPlaceDir() % 4, 2);

	WorldContainer *container = m_OwnerActor->getWorld()->getContainerMgr()->getContainer(blockpos);
	if (container)
	{
		bool bNeedAdd = true;
		for (int i = 0; i < 3; i++)
		{
			bNeedAdd = true;
			PackContainer *pack = (PackContainer *)m_Backpack->getContainer(indices[i]);
			for (size_t n = 0; n < pack->m_Grids.size(); n++)
			{
				if (checkIfItemHasAttAction(pack->m_Grids[n].getItemID(), PlayerItemAttType::Disable_Drop))
					continue;

				if (bNeedAdd && container->onInsertItem(pack->m_Grids[n], pack->m_Grids[n].getNum(), 0) == 0)
				{
					bNeedAdd = false;
					//break;
				}

				if (!pack->m_Grids[n].isEmpty())
				{
					pack->m_Grids[n].clear();
					pack->afterChangeGrid(pack->m_Grids[n].getIndex());
				}
			}
		}
		//m_Backpack->clearPack();
	}
}

void PlayerAttrib::dropEquipItemsToJar(int itemid) 
{
	if (!m_OwnerActor)
		return;

	World* pworld = m_OwnerActor->getWorld();
	if (!pworld)
		return;

	bool isPkgEmpty = true;
	WCoord blockpos = CoordDivBlock(m_OwnerActor->getPosition());
	int blockid = pworld->getBlockID(blockpos);
	auto blockDef = GetDefManagerProxy()->getBlockDef(blockid);
	if (!blockDef)
		return;

	if (!blockDef->Replaceable)
	{
		blockpos += WCoord(0, 1, 0); //往上找一格子
	}

	pworld->setBlockAll(blockpos, itemid, m_OwnerActor->getCurPlaceDir() % 4, 2);

	int indices[] = { m_Backpack->getShortcutStartIndex(), BACKPACK_START_INDEX, EQUIP_START_INDEX };
	WorldContainer* container = pworld->getContainerMgr()->getContainer(blockpos);
	if (container)
	{
		container->m_OwnerUin = m_OwnerActor->getObjId(); //记录归属玩家ID
		bool bNeedAdd = true;
		for (int i = 0; i < 3; i++)
		{
			PackContainer* pack = (PackContainer*)m_Backpack->getContainer(indices[i]);
			for (size_t n = 0; n < pack->m_Grids.size(); n++)
			{
				if (checkIfItemHasAttAction(pack->m_Grids[n].getItemID(), PlayerItemAttType::Disable_Drop))
					continue;

				auto def = GetDefManagerProxy()->getItemDef(pack->m_Grids[n].getItemID());
				if (def && pack->m_Grids[n].getNum() > 0)
				{
					container->onInsertItem(pack->m_Grids[n], pack->m_Grids[n].getNum(), 0);
					isPkgEmpty = false;
				}
				
				if (!pack->m_Grids[n].isEmpty())
				{
					pack->m_Grids[n].clear();
					pack->afterChangeGrid(pack->m_Grids[n].getIndex());
				}
			}
		}
	}

	if(isPkgEmpty)
	{
		m_OwnerActor->getWorld()->setBlockAll(blockpos, 0, 0, 2); //如果没有道具则移除罐子
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("refresh_map", MNSandbox::SandboxContext(nullptr).SetData_Number("type", MRT_Death_Jar));
	}
}
void PlayerAttrib::updateStrength()
{
	float max = m_fBasicMaxStrength;
	max += LivingAttrib::getActorAttValueWithStatus(BUFFATTRT_STRENGTH, max);

	if (max < 0.0f) { max = 0.0f; }

	setMaxStrength(max);
}

float PlayerAttrib::getStrength()
{
	return m_fStrength;
}

float PlayerAttrib::getBasicMaxStrength()
{
	return m_fBasicMaxStrength;
}

float PlayerAttrib::getMaxStrength()
{
	return m_fMaxStrength;
}

float PlayerAttrib::getBasicOverflowStrength()
{
	return m_fBasicOverflowStrength;
}

float PlayerAttrib::getOverflowStrength()
{
	return m_fOverflowStrength;
}

void PlayerAttrib::updateOverflowStrength()
{
	float overflow = m_fBasicOverflowStrength;
	const float buff = LivingAttrib::getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_OVERFLOW_STRENGTH, overflow);
	overflow += buff;
	//code-by:hanyunqiang 天赋2增加拓展体力
	float geniadd = getGeniusValue(GENIUS_SURVIVE, 1);
	overflow *= (1.0f + geniadd);
	if (overflow < 0.0f) { overflow = 0.0f; }

	setOverflowStrength(overflow);
}

void PlayerAttrib::addStrength(float increment)
{
	if (Abs(increment) <= 0.0001f)
	{
		return;
	}
	for (auto p : m_StrengthExecute)
	{
		auto ex = getExtraExecute(p);
		if (ex && ex->ExecuteAdd(this, increment, false, ActorAttribType_Strength))
		{
			return;
		}
	}
	LOG_INFO("PlayerAttrib::addStrength(): uin = %d | m_fStrength = %.2f | increment = %.2f", m_ClientPlayer->getUin(), m_fStrength, increment);
	setStrength(getStrength() + increment);
}

void PlayerAttrib::setStrength(float value)
{
	// //LOG_INFO("PlayerAttrib::setStrength(): uin = %d | %.3f -> %.3f", m_ClientPlayer->getUin(), m_fStrength, value);
	// for (auto p : m_StrengthExecute)
	// {
	// 	auto ex = getExtraExecute(p);
	// 	if (ex && ex->ExecuteSet(this, value, false, ActorAttribType_Strength))
	// 	{
	// 		return;
	// 	}
	// }

	// const bool isGodMode = GetWorldManagerPtr() ? GetWorldManagerPtr()->isGodMode() : false;
	// if (isGodMode)
	// {
	// 	removeBuff(93);
	// 	return;
	// }
	// if (!m_bUseCompatibleStrength || m_StrengthFoodShowState != SFS_Strength)
	// {
	// 	removeBuff(93);
	// 	return;
	// }
	// if (value <= 0)
	// {
	// 	value = 0;
	// }
	// float limit = getMaxStrength() + getOverflowStrength();

	// if (value >= limit)
	// {
	// 	value = limit;
	// }
	// if (Abs(value - m_fStrength) < 0.0001f)
	// {
	// 	return;
	// }
	// const int old = int(m_fStrength);
	// m_fStrength = value;

	// if (!m_ClientPlayer->getWorld() || !m_ClientPlayer->getWorld()->isRemoteMode())
	// 	m_ClientPlayer->syncAttr(ATTRT_CUR_STRENGTH, value);

	// //LOG_INFO("PlayerAttrib::setStrength(): m_fStrength = %4.3f | m_bIsExhausted = %d", m_fStrength, m_bIsExhausted);
	// //1点以下代替清零，兼容整数UI显示问题
	// //if (m_fStrength < 1.f)

	// bool isEnterHomeLand = (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD);
	// if (m_fStrength < m_fMaxStrengthForExhaustion && !isEnterHomeLand)
	// {
	// 	//if (!m_bIsExhausted)
	// 	//{
	// 	//	onExhaustion();
	// 	//}
	// 	if (!getAttrShapeShift())
	// 	{
	// 		m_bIsExhausted = true;
	// 		addBuff(93, 1);
	// 	}
	// }
	// else
	// {
	// 	m_bIsExhausted = false;
	// 	removeBuff(93);
	// }

	// //仅整数部分变化时下发通知
	// if (int(value) != old)
	// {
	// 	if (m_ClientPlayer->hasUIControl())
	// 	{
	// 		//ge GetGameEventQue().postPlayerAttrChange();
	// 		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
	// 		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
	// 			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
	// 	}
			
		
	// 	if (g_WorldMgr && m_OwnerActor)
	// 		g_WorldMgr->signChangedToSync(m_OwnerActor->getObjId(), BIS_STRENGTH);
	// }

	// checkPersistentOperation();
}

void PlayerAttrib::setBasicMaxStrength(float max)
{
	if (max < 0.0f)
	{
		ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();
		if (lua_const)
		{
			m_fBasicMaxStrength = lua_const->strengthmax;
		}
		else
		{
			m_fBasicMaxStrength = 100.0f;
		}
	}
	else
	{
		m_fBasicMaxStrength = max;
	}
	updateStrength();
}

void PlayerAttrib::setMaxStrength(float max)
{
	// if (max <= 0)max = 1;
	// m_fMaxStrength = max;

	// if (!m_ClientPlayer->getWorld() || !m_ClientPlayer->getWorld()->isRemoteMode())
	// 	m_ClientPlayer->syncAttr(ATTRT_MAX_STRENGTH, max);

	// float overflow = getOverflowStrength();
	// if (max < overflow)
	// {
	// 	updateOverflowStrength();
	// 	overflow = getOverflowStrength();
	// }
	// if (getStrength() > max + overflow)
	// {
	// 	//LOG_INFO("PlayerAttrib::setMaxStrength(): uin = %d | m_fStrength = %.2f | max = %.2f", m_ClientPlayer->getUin(), m_fStrength, max);
	// 	setStrength(max + overflow);
	// }

	// // 最大体力值变动 通知属性改变
	// if (m_ClientPlayer->hasUIControl())
	// {
	// 	//ge GetGameEventQue().postPlayerAttrChange();
	// 	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
	// 	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
	// 		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
	// }

	// if (g_WorldMgr && m_OwnerActor)
	// 	g_WorldMgr->signChangedToSync(m_OwnerActor->getObjId(), BIS_STRENGTH_MAX);
}

void PlayerAttrib::setBasicOverflowStrength(float overflow)
{
	const float max = getMaxStrength();
	if (overflow > max)
	{
		overflow = max;
	}
	const int old = int(m_fBasicOverflowStrength);
	m_fBasicOverflowStrength = overflow;
	//m_fOverflowStrength = overflow;

	updateOverflowStrength();
}

void PlayerAttrib::setOverflowStrength(float overflow)
{

}

void PlayerAttrib::setStrengthRestoreFactor(float factor)
{
	m_fStrengthRestoreFactor = factor;
}

void PlayerAttrib::setMaxStrengthForExhaustion(float max)
{
	if (max < 0.0f)
	{
		//modified by navy
		ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();
		if (lua_const)
		{
			m_fMaxStrengthForExhaustion = lua_const->max_strength_for_exhaustion;
		}
		else
		{
			m_fMaxStrengthForExhaustion = 15.0f;
		}
	}
	else
	{
		m_fMaxStrengthForExhaustion = max;
	}
}

void PlayerAttrib::setMaxPercentageOfStrengthForExhaustion(float maxPercentage)
{
	if (maxPercentage < 0.0f)
	{
		//modified by navy
		ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();
		if (lua_const)
		{
			m_fMaxPercentageOfStrengthForExhaustion = lua_const->max_percentage_of_strength_for_exhaustion;
		}
		else
		{
			m_fMaxPercentageOfStrengthForExhaustion = 0.15f;
		}
	}
	else
	{
		m_fMaxPercentageOfStrengthForExhaustion = maxPercentage;
	}
}

void PlayerAttrib::addBasicStrengthRestore(float restore)
{
	setBasicStrengthRestore(m_fBasicStrengthRestore + restore);
}

void PlayerAttrib::setBasicStrengthRestore(float restore)
{
	m_fBasicStrengthRestore = restore;
}

float PlayerAttrib::getBasicStrengthRestore()
{
	return m_fBasicStrengthRestore;
}

bool PlayerAttrib::isExhausted()
{
	if (m_bUseCompatibleStrength)
	{
		return false;
	}
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD)
	{
		return false;
	}

	const bool isGodMode = GetWorldManagerPtr() ? GetWorldManagerPtr()->isGodMode() : false;
	if (isGodMode)
	{
		return false;
	}
	m_bIsExhausted = m_fStrength < m_fMaxStrengthForExhaustion;
	return m_bIsExhausted;
}

bool PlayerAttrib::isStrengthEnough(float consumption)
{
	if (!GetLuaInterfaceProxy().get_lua_const()->check_strength_enough)
	{
		return true;
	}
	//处理浮点数精度问题
	if (consumption <= 0.001f)
	{
		return true;
	}
	if (!m_bUseCompatibleStrength || m_StrengthFoodShowState != SFS_Strength)
	{
		return true;
	}
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD)
	{
		return true;
	}

	const bool isGodMode = GetWorldManagerPtr() ? GetWorldManagerPtr()->isGodMode() : false;
	if (isGodMode)
	{
		return true;
	}
	//处理浮点数精度问题
	const bool enough = m_fStrength + 0.001f >= consumption;
	//LOG_INFO("PlayerAttrib::isStrengthEnough(): m_fStrength = %4.3f | consumption = %4.3f | enough = %d", m_fStrength, consumption, enough);
	return enough;
}

void PlayerAttrib::checkPersistentOperation()
{
	if (!isExhausted())
	{
		return;
	}
	PlayerStateManager& playerStateManager = m_ClientPlayer->getPlayStateManager();
	if (playerStateManager.getState(PLAYERSTTYPE_RUN))
	{
		m_ClientPlayer->setRun(false);
	}
}

int PlayerAttrib::getFoodLevel()
{
	return m_FoodValue->GetValue();
}

int PlayerAttrib::getFoodSatLevel()
{
	return m_FoodValue->GetValue();
}

void PlayerAttrib::setFoodLevel(int foodLevel)
{
	LOG_INFO("PlayerAttrib::setFoodLevel(): uin = %d | %d -> %d", m_ClientPlayer->getUin(), m_FoodLevel, foodLevel);
	m_FoodValue->SetValue(1.0f * foodLevel);
}

void PlayerAttrib::setFoodMaxLevel(int maxVal)
{
	if (maxVal <= 0 || !m_OwnerActor)
		return;

	m_FoodValue->SetMaxFood(maxVal);
}

int PlayerAttrib::getFoodMaxLevel()
{
	return m_FoodValue->GetMaxLimitValue();
}

void PlayerAttrib::setBuffFoodSatLevel(float fSatLevel)
{
	
}

void PlayerAttrib::eatFood(int itemid, bool inbackpack)
{
	const FoodDef *def = GetDefManagerProxy()->getFoodDef(itemid);
	if (def == NULL)
	{
		return;
	}

	ClientPlayer *player = static_cast<ClientPlayer *>(m_OwnerActor);
	
	// 获取野外求生天赋加成 TODO: socgame 暂时屏蔽
	float geniadd = 0.0f; //getGeniusValue(GENIUS_SURVIVE, 1);
	float restoreFactor = 1.0f + geniadd;

	// 初始化各属性恢复系数
	float buffMultipliers[5] = { 1.0f, 1.0f, 1.0f, 1.0f, 1.0f }; // 生命,体力,护盾,毅力,氧气

	// 检查食物增益buff
	if(hasBuff(1072, 1))
	{
		const BuffDef* buffDef = GetDefManagerProxy()->getBuffDef(1072, 1);
		if (buffDef)
		{
			int effectID = buffDef->Status.EffInfo[0].CopyID;
			const BuffEffectDef* effDef = GetDefManagerProxy()->getBuffEffectDef(effectID);
			if (effDef)
			{
				int t0 = effDef->EffectParam[0].Default;
				int t1 = effDef->EffectParam[1].Default;
				int t2 = effDef->EffectParam[2].Default;
				float percent = (t2 / 100.0f) + 1.0f;
				
				// 检查食物类型是否匹配
				int foodType = def->Type;
				if (t0 == 101701 || foodType == t0 - 101702)
				{
					// 根据效果类型应用相应的增益
					switch (t1)
					{
						case 101801: // 全部属性
							buffMultipliers[0] = percent;
							buffMultipliers[1] = percent;
							buffMultipliers[2] = percent;
							buffMultipliers[3] = percent;
							buffMultipliers[4] = percent;
							break;
						case 101802: buffMultipliers[0] = percent; break; // 生命
						case 101803: buffMultipliers[1] = percent; break; // 体力
						case 101804: buffMultipliers[2] = percent; break; // 护盾
						case 101805: buffMultipliers[3] = percent; break; // 毅力
						case 101806: buffMultipliers[4] = percent; break; // 氧气
					}
				}
			}
		}
	}

	// 恢复毅力
	if (def->GetInsistence > 0)
	{
		setPerseverance(def->GetInsistence * buffMultipliers[3], false);
	}

	// 恢复生命值
	if (def->HealAmountSpill > 0)
	{
		addHP(def->HealAmountSpill * restoreFactor, true);
	}
	
	if (def->HealAmount != 0)
	{
		//记录伤害类型
		if (m_ClientPlayer)
		{
			OneAttackData atkdata;
			atkdata.atktype = FOOD_KILL;
			m_ClientPlayer->updataLastAttackDataInfo(atkdata, nullptr);
		}

		addHP(def->HealAmount * restoreFactor * buffMultipliers[0]);
	}

	// 恢复护盾
	if (def->GetShield > 0)
	{
		setArmor(def->GetShield * buffMultipliers[2], false);
	}

	// 调整温度
	if (def->AddTemperature != 0)
	{
		addTemperature(def->AddTemperature, false);
	}

	// 添加随机buff
	if (def->RandomBuff > 0)
	{
		int indices[MAX_FOOD_BUFF];
		int count = 0;
		for (int i = 0; i < MAX_FOOD_BUFF; i++)
		{
			if (def->BuffID[i] > 0) indices[count++] = i;
		}
		if (count > 0)
		{
			int i = indices[GenRandomInt(count)];
			addBuffTimeExtended(def->BuffID[i], def->BuffLevel[i], 0);
		}
	}
	else
	{
		for (int i = 0; i < MAX_FOOD_BUFF; i++)
		{
			if (def->BuffID[i] > 0 && GenRandomInt(10000) < def->BuffOdds[i])
				addBuffTimeExtended(def->BuffID[i], def->BuffLevel[i], 0);
		}
	}

	// 恢复氧气
	if (def->GetOxygen > 0) 
	{
		m_Oxygen += def->GetOxygen * buffMultipliers[4];
	}

	// 处理清除buff效果
	if (def->ClearBuff == 1) {
		clearRandomBuff();
	} else if (def->ClearBuff == 2) {
		clearRandomBadBuff();
	} else if (def->ClearBuff == 3) {
		clearBuffByFoodDef(def);
	}

	// 恢复饥饿值
	if (def->AddFood != 0) 
	{
		float newFoodValue = m_FoodValue->GetFoodValue() + (def->AddFood * (1.0f + geniadd));
		m_FoodValue->SetFoodValue(newFoodValue);
	}

	// 恢复口渴
	if (def->HealThirst != 0)
	{
		addThirst(def->HealThirst * restoreFactor * buffMultipliers[1]); 
	}

	// 背包中的物品消耗处理
	if (inbackpack)
	{
		const bool isGodMode = GetWorldManagerPtr() ? GetWorldManagerPtr()->isGodMode() : false;
		if (!isGodMode)
		{
			BackPack *backpack = player->getBackPack();
			int index = player->getCurShortcut() + m_Backpack->getShortcutStartIndex();
			
			// 处理带容器的食物
			if (def->Container > 0)
			{
				if (backpack->getGridNum(index) == 1)
				{
					GridCopyData data;
					data.resid = def->Container;
					data.num = 1;
					backpack->replaceItem_byGridCopyData(data, index);
				}
				else
				{
					backpack->removeItem(index, 1);
					if (backpack->addItem(def->Container, 1) == 0)
					{
						auto dropComponent = player->GetComponent<DropItemComponent>();
						if (dropComponent)
						{
							dropComponent->dropItem(def->Container, 1);
						}
					}
				}
			}
			else 
			{
				// 普通食物直接消耗
				backpack->removeItem(index, 1);
			}

			if (player)
				player->consumeItemOnTrigger(itemid, 1);
		}
	}

	// 发送事件通知
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("itemid", itemid);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("GE_EAT_FOOD", sandboxContext);
}

void PlayerAttrib::drinkWater(int waterVolume, int waterType, int itemid)
{
	if (m_OwnerActor->getWorld()->isRemoteMode()) return;
	ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();
	int randBad = lua_const->waterblock_bad_effect_probability;
	int hunger = lua_const->waterblock_bad_effect_value;
  //  const FoodDef *def = GetDefManagerProxy()->getFoodDef(itemid);
  //  if (def)
  //  {
		////获取修改上面计算值
		//unthirstValuePer = def->HealThirst;
  //  }

    ClientPlayer *player = static_cast<ClientPlayer *>(m_OwnerActor);

	if (waterType == 0)//0水方块
	{
		int randNum = rand() % 100;
		if (randNum <= randBad)
		{
			addFoodLevel(-hunger, false);
		}
	}
	else if (waterType == 1)//1水囊
	{
		//if (itemid == 1111111) {}
	}
	else if (waterType == 2)//2蓄水桶
	{
		//if (itemid == 1111111) {}
	}
	addThirst(waterVolume / 10);

    //// 发送事件
    //MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
    //    SetData_Number("itemid", itemid);
    //if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
    //    MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("GE_DRINK_WATER", sandboxContext);
}

void PlayerAttrib::SetDefenceBuffStatus(int itemId, int status)
{
	int buffid = 0;

	const ItemDef* def = GetDefManagerProxy()->getItemDef(itemId);
	if (def)
	{
		for (int i = 0; i < (int)def->SkillID.size(); i++)
		{
			const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(def->SkillID[i]);
			if (skilldef)
			{
				for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
				{
					ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
					if (functiondef->oper_id == 20)
					{
						buffid = functiondef->func.defanceDamgeReduceFun.gainBuff;
					}
				}
			}
		}
		if (buffid > 0)
		{
			if (status == PLAYEROP_STATUS_BEGIN) {
				addBuff(buffid / 1000, buffid % 1000);
			}
			else {
				removeBuff(buffid / 1000);
			}
		}
	}
}

/*
static int s_StaminaUse[MAX_STAMINA_METHOD] =
{
	12,
	60,
	18,
	160,
	800,
	25,
	300,
	300,
	3000
};*/

void PlayerAttrib::useStamina(STAMINA_METHOD method, float v)
{
	int n = 0;
	ConstAtLua *cl = GetLuaInterfaceProxy().get_lua_const();
	switch (method)
	{
	case STAMINA_WALK:
		n = cl->tili_action_walk;
		break;
	case STAMINA_SPRINT:
		n = cl->tili_action_sprint;
		break;
	case STAMINA_SWIM:
		n = cl->tili_action_swim;
		break;
	case STAMINA_JUMP:
		n = cl->tili_action_jump;
		break;
	case STAMINA_SPRINTJUMP:
		n = cl->tili_action_sprintjump;
		break;
	case STAMINA_DESTROYBLOCK:
		n = cl->tili_action_destroyblock;
		break;
	case STAMINA_ATTACK:
		n = cl->tili_action_attack;
		break;
	case STAMINA_HURT:
		n = cl->tili_action_hurt;
		break;
	case STAMINA_ADDLIFE:
		n = cl->tili_action_food2hp;
		break;
	}
	float s = n * v;

	World* pworld = m_OwnerActor->getWorld();
	const bool isGodMode = GetWorldManagerPtr() ? GetWorldManagerPtr()->isGodMode() : false;
	if (pworld && !isGodMode)
	{
		if (pworld->getCurMapID() >= MAPID_MENGYANSTAR) s *= cl->planet_tili_beilv;

		staminaUsed(s);
	}
}

void PlayerAttrib::staminaUsed(float s)
{

}

void PlayerAttrib::setExp(int exp)
{
	m_Exp = EncodeActorAttr(exp);
	if (m_OwnerActor == g_pPlayerCtrl)
	{
		//ge GetGameEventQue().postPlayerAttrChange();
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
	}
}

int PlayerAttrib::getExp()
{
	return DecodeActorAttr(m_Exp);
}

void PlayerAttrib::addExp(int e)
{
	int oldExp = getExp();
	setExp(getExp() + e);
}

/*****************************************等级经验*****************************************/
PlayerLevelMode::PlayerLevelMode(PlayerAttrib* pOwnerAttr)
{
	m_pOwnerAttr = pOwnerAttr;

	m_nGainedSumExp = 0;
	m_nCurLevel = 0;
	m_nCurExp = 0;
}

void PlayerLevelMode::AddExp(int nExp)
{
	if (m_pOwnerAttr && nExp > 0)
	{
		m_nGainedSumExp += nExp;
		m_nCurExp += nExp;

		int nMaxLevel = m_pOwnerAttr->getMaxLevel();

		if (m_nCurLevel >= nMaxLevel)
		{
			//已经是最高等级
		}
		else
		{
			int nNewLevel = m_nCurLevel;

			for (int i = nNewLevel; i < nMaxLevel; i++)
			{
				int nUpLevelExp = m_pOwnerAttr->getLevelExp(i);

				if (m_nCurExp >= nUpLevelExp)
				{
					//经验够升级
					nNewLevel += 1;
					m_nCurExp -= nUpLevelExp;
				}
				else
				{
					break;
				}
			}

			World* pworld = m_pOwnerAttr->getOwnerActor()->getWorld();

			if (nNewLevel > m_nCurLevel)
			{
				//升级了
				m_nCurLevel = nNewLevel;

				//触发器事件: 玩家升级
				ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_pOwnerAttr->getOwnerActor());
				if (player)
					player->UpgradeForTrigger();

				//UI事件通知
				if (player && player->hasUIControl())
				{
					//ge GetGameEventQue().postPlayerGainLevelExp(1, player->getUin());
					MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
						SetData_Number("state", 1).
						SetData_Number("uin", player->getUin());
					if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
						MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_GAIN_LEVEL_EXP", sandboxContext);
				}
					
			}
			else
			{
				ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_pOwnerAttr->getOwnerActor());

				//UI事件通知
				if (player && player->hasUIControl())
				{
					//ge GetGameEventQue().postPlayerGainLevelExp(2);
					MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
						SetData_Number("state", 2).
						SetData_Number("uin", 0);
					if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
						MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_GAIN_LEVEL_EXP", sandboxContext);
				}
					
			}
		}
	}
}

int PlayerLevelMode::GetCurLevel()
{
	return m_nCurLevel;
}

int PlayerLevelMode::GetCurExp()
{
	return m_nCurExp;
}

/*****************************************************************/
void PlayerAttrib::addLevelModelExp(int nExp)
{
	nExp += getActorAttValueWithStatus(BUFFATTRT_EXP_GAIN_SPEED, nExp);

	if (m_pLevelMode)
		m_pLevelMode->AddExp(nExp);

	if (m_OwnerActor)
	{
		World* pworld = m_OwnerActor->getWorld();
		if (pworld && pworld->onServer())
			m_OwnerActor->syncAttr(ATTRT_LEVELMODE_INCREMENT, (float)nExp);
	}
}

int PlayerAttrib::getCurLevel()
{
	if (m_pLevelMode)
		return m_pLevelMode->GetCurLevel();

	return 0;
}

int PlayerAttrib::getCurLevelExp()
{
	if (m_pLevelMode)
		return m_pLevelMode->GetCurExp();

	return 0;
}

//触发器用
void PlayerAttrib::setCurLevel(int curLevel)
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode())
	{
		if (GetWorldManagerPtr()->getBaseSettingManager() && GetWorldManagerPtr()->getBaseSettingManager()->isOpenLevelModel())
		{
			int nMaxLevel = getMaxLevel();
			curLevel = curLevel > nMaxLevel ? nMaxLevel : curLevel;

			if (m_OwnerActor)
			{
				ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);

				if (player)
				{
					if (m_pLevelMode)
						m_pLevelMode->SetCurLevel(curLevel);

					//UI事件
					if (m_pLevelMode)
					{
						//ge GetGameEventQue().postPlayerGainLevelExp(1, player->getUin());
						MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
							SetData_Number("state", 1).
							SetData_Number("uin", player->getUin());
						if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
							MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_GAIN_LEVEL_EXP", sandboxContext);
					}
						

					//同步给客机
					player->syncLevelMode();
				}
			}
		}
	}
}

//触发器用
void PlayerAttrib::setCurLevelExp(int curExp)
{
	if (m_pLevelMode == NULL)
		return;

	int levelExp = m_pLevelMode->GetCurExp();

	if (curExp > levelExp)
	{
		int nIncrement = curExp - levelExp;
		addLevelModelExp(nIncrement);
	}
	else
	{
		m_pLevelMode->SetCurExp(curExp);

		if (m_OwnerActor)
		{
			ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
			if (player && player->hasUIControl())
			{
				//ge GetGameEventQue().postPlayerGainLevelExp(2);
				MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
					SetData_Number("state", 2).
					SetData_Number("uin", 0);
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
					MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_GAIN_LEVEL_EXP", sandboxContext);
			}
				
		}
	}

	if (m_OwnerActor)
	{
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
		if (player && !player->hasUIControl())
			player->syncLevelMode();
	}
}

int PlayerAttrib::getMaxLevel()
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->getBaseSettingManager())
	{
		int nMaxLevel = GetWorldManagerPtr()->getBaseSettingManager()->getMaxLevel();

		return nMaxLevel;
	}

	return 0;
}

int PlayerAttrib::getLevelExp(int nLevel)
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->getBaseSettingManager())
	{
		int maxLevel = GetWorldManagerPtr()->getBaseSettingManager()->getMaxLevel();

		if (nLevel > maxLevel)
			return 0;

		int exp = GetWorldManagerPtr()->getBaseSettingManager()->getLevelExp(nLevel);

		return exp;
	}

	return 0;
}

int PlayerAttrib::getLevelSumExp(int nLevel)
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->getBaseSettingManager())
	{
		int sum = 0;
		int maxLevel = GetWorldManagerPtr()->getBaseSettingManager()->getMaxLevel();

		nLevel = nLevel > maxLevel ? maxLevel : nLevel;

		for (int i = 0; i <= nLevel; i++)
		{
			sum += GetWorldManagerPtr()->getBaseSettingManager()->getLevelExp(i);
		}

		return sum;
	}

	return 0;
}

/*****************************************玩法模式:玩家基础属性*****************************************/
void PlayerAttrib::initPlayerBaseAttr(int nTeamId, bool revive /* = false */)
{
	if (m_ClientPlayer)
	{
		LOG_INFO("PlayerAttrib::initPlayerBaseAttr(): uin = %d, nTeamId = %d, revive = %d", m_ClientPlayer->getUin(), nTeamId, revive);
	}

	//玩法规则设置: 基础属性
	if (!m_pBaseAttrSetter)
	{
		return;
	}

	if (!GetWorldManagerPtr() || !GetWorldManagerPtr()->getBaseSettingManager() || !GetWorldManagerPtr()->isGameMakerRunMode())
	{
		return;
	}

	m_pBaseAttrSetter->m_nMaxHP = 100;
	m_pBaseAttrSetter->m_nMaxStrength = 100;
	// 复活不重置，元素攻击/防御 物理攻击/防御
	if (!revive) {
		m_pBaseAttrSetter->m_nAttackPhy = 0;
		m_pBaseAttrSetter->m_nAttackMagic = 0;
		m_pBaseAttrSetter->m_nDefPhy = 0;
		m_pBaseAttrSetter->m_nDefMagic = 0;
	}
	m_pBaseAttrSetter->m_nMoveSpeed = 10;

	//队伍权限没有启用则用全局配置
	bool bState = GetWorldManagerPtr()->getBaseSettingManager()->getCustomFuncState(CUSTOM_ATTRS_ENABLE, nTeamId);
	if (!bState)
		nTeamId = -1;

	IGameMode* pGameMode = GetWorldManagerPtr()->getBaseSettingManager();

	const auto defHP = GetDefManagerProxy()->getPlayerAttribCsvDef(PlayerAttributeType::Life);
	const auto defFood = GetDefManagerProxy()->getPlayerAttribCsvDef(PlayerAttributeType::Food);
	const auto defThirst = GetDefManagerProxy()->getPlayerAttribCsvDef(PlayerAttributeType::Thirst);

	m_pBaseAttrSetter->m_nMaxHP = defHP->MaxVal;
	m_pBaseAttrSetter->m_nMaxStrength = 100;
	m_pBaseAttrSetter->m_nMaxFoodLevel = defFood->MaxVal;
	m_pBaseAttrSetter->m_nMaxThirst = defThirst->MaxVal;  // 从游戏设置获取口渴值上限
	m_pBaseAttrSetter->m_nMoveSpeed = (int)pGameMode->getPlayerBaseAttr(PATTR_SPEED, nTeamId);
	// 复活不重置，元素攻击/防御 物理攻击/防御（触发器会修改这几个值）
	if (!revive)
	{
		m_pBaseAttrSetter->m_nAttackPhy = (int)pGameMode->getPlayerBaseAttr(PATTR_ATTACKPHY, nTeamId);
		m_pBaseAttrSetter->m_nAttackMagic = (int)pGameMode->getPlayerBaseAttr(PATTR_ATTACKELEM, nTeamId);
		m_pBaseAttrSetter->m_nDefPhy = (int)pGameMode->getPlayerBaseAttr(PATTR_DEFPHY, nTeamId);
		m_pBaseAttrSetter->m_nDefMagic = (int)pGameMode->getPlayerBaseAttr(PATTR_DEFELEM, nTeamId);
	}

	float maxHP = (float)m_pBaseAttrSetter->m_nMaxHP;
	float maxStrength = (float)m_pBaseAttrSetter->m_nMaxStrength;

	setBasicMaxHP(defHP->MaxVal);
	setBasicMaxStrength(100);
	setFoodMaxLevel(defFood->MaxVal);
	setMaxThirst(defThirst->MaxVal);

	//移动速度
	setSpeedAtt(Actor_Walk_Speed, (float)m_pBaseAttrSetter->m_nMoveSpeed);
	setSpeedAtt(Actor_Run_Speed, (float)m_pBaseAttrSetter->m_nMoveSpeed);
	setSpeedAtt(Actor_Sneak_Speed, (float)m_pBaseAttrSetter->m_nMoveSpeed);
	setSpeedAtt(Actor_Swim_Speed, (float)m_pBaseAttrSetter->m_nMoveSpeed);

	// 是否使用体力
	int useCompatibleStrength = false;
	//LOG_INFO("PlayerAttrib::initPlayerBaseAttr():");
	toggleUseCompatibleStrength(useCompatibleStrength);
	int strengthFoodShowState = 0;
	setStrengthFoodShowState(strengthFoodShowState);

	if (!GetWorldManagerPtr()->m_RuleMgr)
	{
		return;
	}
	//0:保存 1:不保存
	// int val = (int)GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE);
	// if (val == 1)
	// {
	// 	if (!isDead()) // 死亡状态不要重置生命值
	// 	{
	// 		setHP(maxHP); //重置生命最为最大生命值
	// 		setStrength(maxStrength);
	// 		setFoodLevel(m_pBaseAttrSetter->m_nMaxFoodLevel);
	// 		setThirst(m_pBaseAttrSetter->m_nMaxThirst);  // 重置口渴值
	// 		if (GetLuaInterfaceProxy().shouldUseNewHpRule())// 重置地图重置护甲和毅力值
	// 		{
	// 			setArmor(0);
	// 			setPerseverance(0);
	// 		}
	// 	}
	// }
}

int PlayerAttrib::getPlayerBaseAttr(int attrType)
{
	if (!m_pBaseAttrSetter)
	{
		return 0;
	}
	if (!GetWorldManagerPtr())
	{
		return 0;
	}
	if (!GetWorldManagerPtr()->getBaseSettingManager())
	{
		return 0;
	}
	if (!GetWorldManagerPtr()->isGameMakerRunMode())
	{
		return 0;
	}
	PRIME_ATTR_TYPE type = (PRIME_ATTR_TYPE)attrType;

	switch (type)
	{
	case PATTR_HP:
		return m_pBaseAttrSetter->m_nMaxHP;
	case PATTR_STRENGTH:
		return m_pBaseAttrSetter->m_nMaxStrength;
	case PATTR_HUNGER:
		return m_FoodValue->GetMaxFood();
	case PATTR_THIRST:
		return m_ThirstValue->GetMaxThirst();
	case PATTR_SPEED:
		return m_pBaseAttrSetter->m_nMoveSpeed;
	case PATTR_ATTACKPHY:
		return m_pBaseAttrSetter->m_nAttackPhy;
	case PATTR_ATTACKELEM:
		return m_pBaseAttrSetter->m_nAttackMagic;
	case PATTR_DEFPHY:
		return m_pBaseAttrSetter->m_nDefPhy;
	case PATTR_DEFELEM:
		return m_pBaseAttrSetter->m_nDefMagic;
	}

	return 0;
}

void PlayerAttrib::setPlayerBaseAttr(int attrType, int val)
{
	LOG_INFO("PlayerAttrib::setPlayerBaseAttr(): attrType = %d, val = %d", attrType, val);

	if (!m_pBaseAttrSetter)
	{
		return;
	}
	if (!GetWorldManagerPtr())
	{
		return;
	}
	if (!GetWorldManagerPtr()->getBaseSettingManager())
	{
		return;
	}
	if (!GetWorldManagerPtr()->isGameMakerRunMode())
	{
		return;
	}

	PRIME_ATTR_TYPE type = (PRIME_ATTR_TYPE)attrType;
	switch (type)
	{
	case PATTR_HUNGER:
		m_pBaseAttrSetter->m_nMaxFoodLevel = val;
		break;
	case PATTR_THIRST:
		m_pBaseAttrSetter->m_nMaxThirst = val;
		break;
	case PATTR_ATTACKPHY:
		m_pBaseAttrSetter->m_nAttackPhy = val;
		break;
	case PATTR_ATTACKELEM:
		m_pBaseAttrSetter->m_nAttackMagic = val;
		break;
	case PATTR_DEFPHY:
		m_pBaseAttrSetter->m_nDefPhy = val;
		break;
	case PATTR_DEFELEM:
		m_pBaseAttrSetter->m_nDefMagic = val;
		break;
	}
}

static std::string getRuneEffect(BackPackGrid* grid, bool& isLightning) {
	std::string ret;
	isLightning = false;
	if (!grid)
		return ret;
	const GridRuneData& rune = grid->getRuneData();
	//只取一个最高等级的
	int highestLevel = 0;
	for (int i = 0; i < rune.getRuneNum(); i++)
	{
		const GridRuneItemData& runeitem = rune.getItemByIndex(i);
		int runelevel = runeitem.rune_id % 100;
		if (runelevel >= highestLevel) {
			highestLevel = runelevel;
			auto* runDef = GetDefManagerProxy()->getRuneDef(runeitem.rune_id);
			if (runDef)
			{
				ret = runDef->effect;
				if (runDef->EnchantType == ENCHANT_LIGHTNING_CHAIN) {
					isLightning = true;
				}
			}
			if (ret.empty())
			{
				ret = getHandRuneEffectName(runeitem.item_id);
				isLightning = false;
			}
		}
	}
	return ret;
}


static std::vector<const char*> getEnchantEffects(BackPackGrid *grid, int effecttype) {
	std::vector<const char*> ret;
	if (!grid)
		return ret;
	//同一装备 附魔/符文不共存
	int numenchant = grid->getNumEnchant();
	if (numenchant > 0) {
		for (int i = 0; i < numenchant; i++)
		{
			const EnchantDef *def = GetDefManagerProxy()->getEnchantDef(grid->getIthEnchant(i));
			if (def == NULL) continue;
			const char* effect = effecttype ? (const char*)def->effect2.c_str() : (const char*)def->effect.c_str();
			if (effect && effect[0])
				ret.push_back(effect);
		}
	}
	return ret;
}

void PlayerAttrib::ApplyWeaponEnchantEffect(ActorBody *body)
{
	BackPackGrid *grid = getEquipGrid(EQUIP_WEAPON);
	if (!grid || !grid->def)
		return;

	DefDataTable<ToolDef> &toolTable = GetDefManagerProxy()->getToolTable();
	ToolDef *tool = toolTable.GetRecord(grid->def->ID);
	if (!tool)
		return;
	int maxItem = max(MAX_ITEM_RUNES, MAX_ITEM_ENCHANTS);

	bool bCameraNeedPlay = false;
	if (g_pPlayerCtrl && g_pPlayerCtrl->GetActor() == m_OwnerActor && g_pPlayerCtrl->m_CameraModel)
	{
		bCameraNeedPlay = true;
	}
	 

	for (int i = 0; i < maxItem; i++)
	{
		if (body)
			body->stopWeaponMotion(10000 + i);

		if (bCameraNeedPlay)
		{
			g_pPlayerCtrl->m_CameraModel->stopWeaponMotion(10000 + i);
		}
	}
	//codeby:crane
	bool bBodyNeedPlay = body
		&& body->getModel()
		&& body->getModel()->IsShow();

	float scale = 1.0f;
	if (grid->getRuneData().getRuneNum() > 0) {//有符文  走符文逻辑
		bool isLightning = false;
		std::string runeEffectName = getRuneEffect(grid, isLightning);
		if (runeEffectName.size() > 0) {
			if (isLightning)
			{
				scale = GetLuaInterfaceProxy().get_lua_const()->lightningWeaponEffectScale;
			}
			float fpsScale = scale * 1.5f;
			if (bBodyNeedPlay && body)
			{
				BaseItemMesh* weaponmodel = dynamic_cast<BaseItemMesh*>(body->getWeaponModel());
				if (weaponmodel)
				{
					(weaponmodel)->playMotion(0,runeEffectName.c_str(), true, 10000, scale);
				}
			}

			if (bCameraNeedPlay)
				g_pPlayerCtrl->m_CameraModel->playWeaponMotion(runeEffectName.c_str(), true, 10000, fpsScale);
		}
		return;
	}

	if (tool->EnchantEffectScale % 10000)
		scale = ((float)(tool->EnchantEffectScale % 10000)) / 100.0f;

	int effecttype = tool->EnchantEffectScale / 10000;
	if (effecttype >= 2)
		return;

	std::vector<const char*> allEffects = getEnchantEffects(grid, effecttype);

	for (unsigned int i = 0; i < allEffects.size(); i++) {
		const char* effect = allEffects[i];
		if (bBodyNeedPlay && body)
			body->playWeaponMotion(effect, true, 10000 + i, scale);

		if (bCameraNeedPlay)
			g_pPlayerCtrl->m_CameraModel->playWeaponMotion(effect, true, 10000 + i, scale * 1.5f);
	}

}

static void BroadcastBodyWeaponEffect(ClientActor *actor, const char* effectName, int effectID, bool status, int scale)
{
	World *pworld = actor->getWorld();
	//主机才去做广播
	if (pworld->onServer())
	{
		PB_PlayWeaponEffectHC playWeaponEffectHC;
		playWeaponEffectHC.set_objid(actor->getObjId());
		playWeaponEffectHC.set_effectid(effectID);
		playWeaponEffectHC.set_effectstatus(status);
		playWeaponEffectHC.set_effectscale(scale);
		playWeaponEffectHC.set_effectname(effectName);

		pworld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYWEAPONEFFECT_HC, playWeaponEffectHC, actor, true);
	}
}

void PlayerAttrib::SyncWeaponEnchantEffect()
{
	BackPackGrid *grid = getEquipGrid(EQUIP_WEAPON);

	if (!grid || !grid->def)
		return;
	DefDataTable<ToolDef> &toolTable = GetDefManagerProxy()->getToolTable();
	ToolDef *tool = toolTable.GetRecord(grid->def->ID);
	if (!tool)
		return;
	int maxItem = max(MAX_ITEM_RUNES, MAX_ITEM_ENCHANTS);
	for (int i = 0; i < maxItem; i++)
	{
		World *pworld = m_OwnerActor->getWorld();
		if (pworld && m_OwnerActor->getBody() && pworld->onServer())
		{
			BroadcastBodyWeaponEffect(m_OwnerActor, "", 10000 + i, 1, 100);
		}
	}

	int scale = 100;
	World *pworld = m_OwnerActor->getWorld();
	bool bNeedBroadcast = pworld && m_OwnerActor->getBody() && pworld->onServer();

	if (grid->getRuneData().getRuneNum() > 0) {//有符文  走符文逻辑
		bool isLightning = false;
		std::string runeEffectName = getRuneEffect(grid, isLightning);
		if (runeEffectName.size() > 0) {
			BroadcastBodyWeaponEffect(m_OwnerActor, runeEffectName.c_str(), 10000, 0, scale);
		}
		return;
	}

	if (tool->EnchantEffectScale % 10000)
		scale = tool->EnchantEffectScale % 10000;

	int effecttype = tool->EnchantEffectScale / 10000;
	if (effecttype >= 2)
		return;

	if (bNeedBroadcast) {
		std::vector<const char*> allEffects = getEnchantEffects(grid, effecttype);
		for (unsigned int i = 0; i < allEffects.size(); i++) {
			const char* effect = allEffects[i];
			BroadcastBodyWeaponEffect(m_OwnerActor, effect, 10000 + i, 0, scale);
		}
	}
}

void PlayerAttrib::toggleUseCompatibleStrength(bool enable)
{
	return;
	//LOG_INFO("toggleUseCompatibleStrength(): enable = %d", enable);
	m_bUseCompatibleStrength = enable;
	if (enable)
	{
		m_fRecover = GetLuaInterfaceProxy().get_lua_const()->default_xieliang_huifu_beilv;
	}
	else
	{
		m_fRecover = GetLuaInterfaceProxy().get_lua_const()->default_xieliang_huifu_beilv_old;
		removeBuff(93);
	}
}

bool PlayerAttrib::useCompatibleStrength()
{
	return m_bUseCompatibleStrength;
}

void PlayerAttrib::setStrengthFoodShowState(int flag)
{
	m_StrengthFoodShowState = flag;
}

int PlayerAttrib::strengthFoodShowState()
{
	return m_StrengthFoodShowState;
}


float PlayerAttrib::getFood() {
	return m_FoodValue->GetFoodValue();
}

float PlayerAttrib::getMaxFood() {
	return m_FoodValue->GetMaxFood();
}

float PlayerAttrib::getBasicMaxThirst()
{
	//return m_fBasicMaxThirst;
	return m_ThirstValue->GetBasicMaxThirst();
}

float PlayerAttrib::getThirst()
{
	if (m_ThirstValue->GetThirstValue() == std::numeric_limits<float>::min()) {
		return 0;
	}
	return m_ThirstValue->GetThirstValue();
}

float PlayerAttrib::getMaxThirst()
{
	return m_ThirstValue->GetMaxThirst();
}

float PlayerAttrib::getBasicOverflowThirst()
{
	//return m_fBasicOverflowThirst;
	return m_ThirstValue->GetBasicOverflowThirst();
}

float PlayerAttrib::getOverflowThirst()
{
	//return m_fOverflowThirst;
	return m_ThirstValue->GetOverflowThirst();
}

#ifdef IWORLD_DEV_BUILD
std::string PlayerAttrib::toString()
{
	ostringstream oss;
	oss << ActorAttrib::toString()
		<< " | strength = " << m_fStrength << '/' << m_fMaxStrength + m_fOverflowStrength << '(' << m_fMaxStrength << '+' << m_fOverflowStrength << ')';
	return oss.str();
}
#endif

void PlayerAttrib::tick()
{
	World* world = m_OwnerActor->getWorld();
	if (!world) return;

	if (m_StarDebuffTime > 0)
	{
		m_StarDebuffTime--;
		if (m_StarDebuffTime == 0) setStarDebuffStage(0);
	}
	// 更新倒地状态
	if (m_pDownedStateAttrib)
	{
		m_pDownedStateAttrib->tick();
	}
	if (m_Life < 0)
	{
		return;
	}

	LivingAttrib::tick();

	if (!m_bInitialized) {
		return;
	}

	//创造模式和开发者编辑模式 关闭血量/体力的恢复/消耗等机制
	const bool isGodMode = GetWorldManagerPtr() ? GetWorldManagerPtr()->isGodMode() : false;
	if (isGodMode) {
		return;
	}
	if (world->onServer())
	{
		uint64_t currentTime = GetWorldManagerPtr()->getCurrentTimeStamp();
		if (currentTime - m_lastTickTime >= 1000)
		{
			if (m_lastTickTime > 0) {
				foodTick_Server();
				thirstTick_Server();
				temperatureTick_Server();
				radiationTick_Server();
			}
			m_lastTickTime = currentTime;
		}

		backPackTick();
	} else {
		uint64_t currentTime = GetWorldManagerPtr()->getCurrentTimeStamp();
		if (currentTime - m_lastTickTimeClient >= 1000) {
			foodTick_Client();
			thirstTick_Client();
			temperatureTick_Client();
			radiationTick_Client();
			m_lastTickTimeClient = currentTime;
		}
	}

	if (world->onServer())
	{
		if (getAttrShapeShift())
			m_FoodLevel = m_oldFoodLevel;

		waterPressureTick();
	}

	if (m_AttrShapeShiftTick < 2)
	{
		m_AttrShapeShiftTick++;
	}

	if (getAttrShapeShift())
	{
		if (m_AttrShapeShiftTick == 1)
			setAttrShapeShift(false);
		if (m_bIsExhausted)
		{
			m_bIsExhausted = false;
			removeBuff(93);
		}
	}

	// 制作队列tick
	m_CraftingQueue->tick();
}

bool PlayerAttrib::isStill()
{
	ActorBody* actorBody = m_OwnerActor->getBody();
	if (!actorBody)
	{
		return false;
	}

	//排除卡墙问题
	const bool animationStill =
		!actorBody->hasAnimPlaying(SEQ_RUN)
		&& !actorBody->hasAnimPlaying(SEQ_SWIM)
		&& !actorBody->hasAnimPlaying(SEQ_SWIM_DIVING)
		&& !actorBody->hasAnimPlaying(SEQ_SWIM_IDLE)
		&& !actorBody->hasAnimPlaying(SEQ_SWIM_RUSH)
		;

	const bool still =
		m_ClientPlayer->getPlayStateManager().getState(PLAYERSTTYPE_STOP)
		&& m_ClientPlayer->getCurOperate() == PLAYEROP_NULL
		&& animationStill
		;

	return still;
}

void PlayerAttrib::hpRestoreTick()
{
	if (!isStill())
	{
		m_StillnessTick = 0;
		return;
	}

	++m_StillnessTick;
	//1秒1次
	if (m_StillnessTick < MIN_STILL_TICK)
	{
		return;
	}
	//避免两次动画冲突
	if (m_Life >= getLimitHP())
	{
		m_StillnessTick = 0;
		return;
	}
	m_StillnessTick = 0;
	float restoreFactor = getHPRecover();
	//code-by:hanyunqiang//野外求生 回复速度增加
	restoreFactor += getGeniusValue(GENIUS_SURVIVE, 0);

	float restore = 1 * restoreFactor;

	LivingAttrib* pAttrib = dynamic_cast<LivingAttrib*>(this);
	if (pAttrib)
	{
		restore += pAttrib->getActorAttValueWithStatus(BUFFATTRT_HP_RECOVER_SPEED, restore);
	}

	addHP(restore);
	LOG_INFO("PlayerAttrib::hpRestoreTick(): uin = %d | HP = %.2f | restore = %.2f", m_ClientPlayer->getUin(), m_Life, restore);
}

bool PlayerAttrib::needConsumingStrength()
{
	// PlayerStateManager& playerStateManager = m_ClientPlayer->getPlayStateManager();
	// const bool inRunning = playerStateManager.getState(PLAYERSTTYPE_RUN);
	// const bool isMoving = playerStateManager.getState(PLAYERSTTYPE_MOVE);
	// const bool isFishing = m_ClientPlayer->isFishing();//钓鱼消耗体力
	// const bool need = inRunning
	// 	|| (inRunning && playerStateManager.getState(PLAYERSTTYPE_JUMP))
	// 	|| (inRunning && playerStateManager.getState(PLAYERSTTYPE_JUMPFIRST))
	// 	//|| playerStateManager.getState(PLAYERSTTYPE_JUMPSECOND)
	// 	;
	// //const bool inLiquid = isInLiquid();
	// //LOG_INFO("needConsumingStrength(): %d | %s", op, playerStateManager.toString().c_str());
	// //LOG_INFO("needConsumingStrength(): %d | %s", inLiquid, playerStateManager.toString().c_str());
	// //LOG_INFO("needConsumingStrength(): %s", playerStateManager.toString().c_str());
	// //LOG_INFO("needConsumingStrength(): %d | %d", op, inRunning ? 1 : 0);
	// if (need)
	// {
	// 	return true;
	// }
	// const int op = m_ClientPlayer->getCurOperate();
	// //LOG_INFO("needConsumingStrength(): op = %d", op);
	// switch (op)
	// {
	// case PLAYEROP_DIG:
	// case PLAYEROP_SHOOT:
	// case PLAYEROP_PUSHSNOWBALL_SHOOT:
	// case PLAYEROP_USE_ITEM_SKILL:
	// case PLAYEROP_GRAVITY_CHARGE_BEGIN:
	// 	return true;
	// 	break;
	// case PLAYEROP_ATTACK_BOW:
	// 	if (m_chekckNeedConsumeStrength == false)//蓄力扣除体力优化 renjie
	// 	{
	// 		m_chekckNeedConsumeStrength = true;
	// 		return true;
	// 	}
	// 	return false;
	// }
	// if (isInLiquid() && playerStateManager.getState(PLAYERSTTYPE_MOVE))
	// {
	// 	return true;
	// }
	// //在移动的除了潜行和跑步,只有步行了.跑步和游泳之类的上面已经判断了
	// if (isMoving && !playerStateManager.getState(PLAYERSTTYPE_SNEAK))
	// {
	// 	return true;
	// }
	// if (isFishing)
	// {
	// 	return true;
	// }
	// if (m_chekckNeedConsumeStrength == true)//蓄力扣除体力优化
	// {
	// 	m_chekckNeedConsumeStrength = false;
	// 	return false;
	// }
	return false;
}

bool PlayerAttrib::needConsumingThirst()
{
	// PlayerStateManager& playerStateManager = m_ClientPlayer->getPlayStateManager();
	// const bool inRunning = playerStateManager.getState(PLAYERSTTYPE_RUN);
	// const bool isMoving = playerStateManager.getState(PLAYERSTTYPE_MOVE);
	// const bool isFishing = m_ClientPlayer->isFishing();//钓鱼消耗体力
	// const bool need = inRunning
	// 	|| (inRunning && playerStateManager.getState(PLAYERSTTYPE_JUMP))
	// 	|| (inRunning && playerStateManager.getState(PLAYERSTTYPE_JUMPFIRST))
	// 	//|| playerStateManager.getState(PLAYERSTTYPE_JUMPSECOND)
	// 	;
	// //const bool inLiquid = isInLiquid();
	// //LOG_INFO("needConsumingStrength(): %d | %s", op, playerStateManager.toString().c_str());
	// //LOG_INFO("needConsumingStrength(): %d | %s", inLiquid, playerStateManager.toString().c_str());
	// //LOG_INFO("needConsumingStrength(): %s", playerStateManager.toString().c_str());
	// //LOG_INFO("needConsumingStrength(): %d | %d", op, inRunning ? 1 : 0);
	// if (need)
	// {
	// 	return true;
	// }
	// const int op = m_ClientPlayer->getCurOperate();
	// //LOG_INFO("needConsumingStrength(): op = %d", op);
	// switch (op)
	// {
	// case PLAYEROP_DIG:
	// case PLAYEROP_SHOOT:
	// case PLAYEROP_PUSHSNOWBALL_SHOOT:
	// case PLAYEROP_USE_ITEM_SKILL:
	// case PLAYEROP_GRAVITY_CHARGE_BEGIN:
	// 	return true;
	// 	break;
	// case PLAYEROP_ATTACK_BOW:
	// 	if (m_chekckNeedConsumeThirst == false)//蓄力扣除体力优化 renjie
	// 	{
	// 		m_chekckNeedConsumeThirst = true;
	// 		return true;
	// 	}
	// 	return false;
	// }
	// if (isInLiquid() && playerStateManager.getState(PLAYERSTTYPE_MOVE))
	// {
	// 	return true;
	// }
	// //在移动的除了潜行和跑步,只有步行了.跑步和游泳之类的上面已经判断了
	// if (isMoving && !playerStateManager.getState(PLAYERSTTYPE_SNEAK))
	// {
	// 	return true;
	// }
	// if (isFishing)
	// {
	// 	return true;
	// }
	// if (m_chekckNeedConsumeThirst == true)//蓄力扣除体力优化
	// {
	// 	m_chekckNeedConsumeThirst = false;
	// 	return false;
	// }
	return false;
}

void PlayerAttrib::backPackTick()
{
    auto bagsContainer = m_Backpack;
    if (bagsContainer)
    {
        tickCount++;
        if (tickCount == 20)
        {
            tickCount = 0;
            BackPackGrid* itemgrid = getEquipGrid(EQUIP_WEAPON);
            if (itemgrid && itemgrid->def && itemgrid->def->ID == ITEM_GLOW_STICK)
            {
                int locknum = itemgrid->addDuration(-1);
				if (itemgrid->getDuration() <= 0)
                {
					itemgrid->clear();
                    m_Backpack->addItem(ITEM_GLOW_STICK_USED, 1);
					//刷新手上模型
                    if (g_WorldMgr)
                    {
                        g_WorldMgr->resetAllPlayerHandModel();
                    }
                    if (g_pPlayerCtrl && g_pPlayerCtrl== m_ClientPlayer)
                    {
                        g_pPlayerCtrl->resetHandModel();
                    }
					//同步手持道具使用情况
					m_ClientPlayer->addDirtyIndex(equipSlot2Index(EQUIP_WEAPON));
                }
				else
				{
					//耐久度变化同步给客机 by：Jeff
					if (m_ClientPlayer)
					{
						jsonxx::Object context;

						context << "index" << bagsContainer->getShortcutStartIndex() + m_CurShotcut;
						context << "Duration" << itemgrid->getDuration();
						SandBoxManager::getSingleton().sendToClient(m_ClientPlayer->getUin(), "PB_BACKPACKGRID_DRUATION_HC", context.bin(), context.binLen());
					}
				}
				
				//ge GameEventQue::GetInstance().postBackpackChange(bagsContainer->getShortcutStartIndex() + m_CurShotcut);
				MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
					SetData_Number("grid_index", bagsContainer->getShortcutStartIndex() + m_CurShotcut);
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
					MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
				}
            }
        }

    }
}

bool PlayerAttrib::isChargingForItem()
{
	const int op = m_ClientPlayer->getCurOperate();
	//LOG_INFO("isChargingItem(): op = %d", op);
	switch (op)
	{
	case PLAYEROP_DIG:
		return true;
	case PLAYEROP_ATTACK_BOW:
	case PLAYEROP_USE_ITEM_SKILL:
	case PLAYEROP_GRAVITY_CHARGE_BEGIN:
		break;
	default:
		return false;
	}
	const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(m_ClientPlayer->getCurToolID());
	if (!tooldef)
	{
		return false;
	}
	return tooldef->AccumulatorType == 0 || tooldef->AccumulatorType == 1;
}

bool PlayerAttrib::isEquipOxygenpack()
{
	if (getEquipItemWithType(EQUIP_PIFENG) == ITEM_OXYGEN_PACK)
	{
		return true;
	}

	return false;
}

bool PlayerAttrib::damageOxygenpack(int value)
{
	if (!isEquipOxygenpack())
		return false;

	damageEquipItemWithType(EQUIP_PIFENG, value);

	return true;
}

bool PlayerAttrib::isInLiquid()
{
	ActorLocoMotion* motion = m_OwnerActor->getLocoMotion();
	return motion != NULL && motion->isInLiquid();
}

float PlayerAttrib::getChargeConsumptionPerSecond()
{
	const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(m_ClientPlayer->getCurToolID());
	if (m_ClientPlayer->getCurOperate() == PLAYEROP_DIG)
	{
		float strength = GetLuaInterfaceProxy().get_lua_const()->strength_consumption_of_digging_per_second;
		if (g_pPlayerCtrl)
		{
			auto blockDef = GetDefManagerProxy()->getBlockDef(g_pPlayerCtrl->getDigBlockID());
			if (blockDef && blockDef->Hardness > 10)
			{
				if (m_ClientPlayer->getCurToolID() == 0 || !tooldef || tooldef && (tooldef->Type != blockDef->MineTool || tooldef->Level < blockDef->ToolLevel))
				{
					strength = strength * 3 * log10(blockDef->Hardness);
				}
			}
		}
		return strength;
	}
	if (!tooldef)
	{
		return 0;
	}
	float consumption = tooldef->AccumulatorExpend;
	return consumption;
}

void PlayerAttrib::checkNeedConsumeStrength()
{
	if (m_ClientPlayer)
	{
		const int op = m_ClientPlayer->getCurOperate();
		switch (op)
		{
		case PLAYEROP_ATTACK_BOW:
			break;
		default:
			if (m_chekckNeedConsumeStrength == true)//蓄力扣除体力优化
			{
				m_chekckNeedConsumeStrength = false;
			}
		}
	}
	
}
void PlayerAttrib::checkNeedConsumeThirst()
{
	if (m_ClientPlayer)
	{
		const int op = m_ClientPlayer->getCurOperate();
		switch (op)
		{
		case PLAYEROP_ATTACK_BOW:
			break;
		default:
			if (m_chekckNeedConsumeThirst == true)//蓄力扣除体力优化
			{
				m_chekckNeedConsumeThirst = false;
			}
		}
	}
	
}

void PlayerAttrib::strengthTick()
{

}

void PlayerAttrib::onExhaustion()
{
	if (isChargingForItem())
	{
		m_ClientPlayer->endCurOperate();
	}
}

void PlayerAttrib::temperatureTick_Server()
{
	if (!g_WorldMgr || !m_OwnerActor) return;

	bool bUpdate = true;
	if (!g_WorldMgr->getTemperatureMgr()->GetTemperatureActive()) {
		bUpdate = false;
	}

	World* world = m_OwnerActor->getWorld();
	if (!world) return;

	if (bUpdate) {

		TemperatureManager* tempMgr = static_cast<TemperatureManager*>(g_WorldMgr->getTemperatureMgr());
		WCoord pos = m_ClientPlayer->iGetPosition();
		float oldTemp = m_Temperature;
		
		float envTemp = tempMgr->GetPositionTemperature(world, pos);
		float wetness = world->getBiome(pos.x, pos.z)->Humid / 100.0f;
		float bodyTemp = getTemperature();

		float insulation = getArmorPointByPart(ATTACK_TEMPERATURE, ATTACK_BODY_MAX);
		float tempChange = (envTemp - bodyTemp) * (1.0 - insulation);
		float deltaTemp = tempChange;
		
		float adjustRate = deltaTemp / 40.0 * (1.0 + wetness * 1.5);
		adjustRate = clamp(adjustRate, -1.0f, 1.0f);
		bodyTemp += adjustRate;

		m_Temperature = clamp(bodyTemp, TEMPERATURE_BODY_MIN, TEMPERATURE_BODY_MAX);
		if (oldTemp != m_Temperature)
		{
			m_ClientPlayer->syncAttr(ATTRT_TEMPERATURE, m_FinalPosTemperature);
		}
	}

	checkAttributeBuffs(PlayerAttributeType::Temperature);

// 	float t1 = getTemperature();
// 	auto tmperatureMgr = static_cast<TemperatureManager*>(g_WorldMgr->getTemperatureMgr());
	
// 	float t2 = tmperatureMgr->GetEnviromentTemperature(world, m_ClientPlayer->iGetPosition());
// 	LOG_INFO("temperature: uin = %d | body = %.2f | env = %.2f", m_ClientPlayer->getUin(), t1, t2); 
}

void PlayerAttrib::temperatureTick_Client()
{
#ifndef IWORLD_SERVER_BUILD
	if (!g_WorldMgr || !m_OwnerActor) return;

	static bool isShowLowTemperatureTips = false;
	ConstAtLua* pLuaConst = GetLuaInterfaceProxy().get_lua_const();
	if (m_Temperature <= pLuaConst->K_TEMPERATURE_LOW_TIPS && !isShowLowTemperatureTips) {
		showAttributeTips();
		isShowLowTemperatureTips = true;
	}
	else if (m_Temperature > pLuaConst->K_TEMPERATURE_LOW_TIPS) {
		isShowLowTemperatureTips = false;
	}
#endif
}

void PlayerAttrib::radiationTick_Server()
{
	if (!g_WorldMgr || !m_OwnerActor) return;

	RadiationManager* radMgr = static_cast<RadiationManager*>(g_WorldMgr->getRadiationMgr());

	bool bUpdate = true;
	if (!radMgr->GetRadiationActive()) {
		bUpdate = false;
	}

	World* world = m_OwnerActor->getWorld();
	if (!world) return;


	if (bUpdate) {
		const PlayerAttribCsvDef* config = GetDefManagerProxy()->getPlayerAttribCsvDef(PlayerAttributeType::Radiation);
    	if (!config) return;
		const float minRadHurt = 0.1f;
		float recoveryRate = config->Recovery;
		float oldRadiation = m_Radiation;
		float envRad = radMgr->GetEnviromentRadiation(world, m_ClientPlayer->iGetPosition());
		float inRangeRad = radMgr->GetRadiationInRange(world, m_ClientPlayer->iGetPosition());
		float resistance = getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_RADIATION_RESISTANCE, 0);
		float reduce = getArmorPointByPart(ATTACK_RADIATION, ATTACK_BODY_MAX);
		float change = (envRad + inRangeRad) * (1.0f - resistance) * (1.0f - reduce);
		if (change < minRadHurt) {
			uint64_t currentTime = g_WorldMgr->getCurrentTimeStamp();
			if (currentTime - m_lastRadiationRecoverTime > 1000) {
				m_Radiation -= recoveryRate;
				if (m_Radiation < 0) {
					m_Radiation = 0;
				}
				m_lastRadiationRecoverTime = currentTime;
			}
		} else {
			m_Radiation += change;
			if (m_Radiation > m_MaxRadiation) {
				m_Radiation = m_MaxRadiation;
			}
		}
		
		if (oldRadiation != m_Radiation)
		{
			m_ClientPlayer->syncAttr(ATTRT_RADIATION, m_Radiation);
		}
	}

	checkAttributeBuffs(PlayerAttributeType::Radiation);

	//if (true) {
	//	float r1 = getRadiation();
	//	float r2 = radMgr->GetEnviromentRadiation(world, m_ClientPlayer->getPosition());
	//	float r3 = radMgr->GetRadiationInRange(world, m_ClientPlayer->getPosition());
	//	LOG_INFO("PlayerAttrib::radiationTick(): uin = %d | body rad = %.2f | env rad = %.2f | in range rad = %.2f", m_ClientPlayer->getUin(), r1, r2, r3);
	//}
}

void PlayerAttrib::radiationTick_Client()
{
#ifndef IWORLD_SERVER_BUILD
	static bool isShowLowRadiationTips = false;
	ConstAtLua* pLuaConst = GetLuaInterfaceProxy().get_lua_const();
	if (m_Radiation >= pLuaConst->K_RADIATION_HIGH_TIPS && !isShowLowRadiationTips) {
		showAttributeTips();
		isShowLowRadiationTips = true;

		RadiationComponent* pComp = m_ClientPlayer->getRadiationComponent();
		if (pComp) {
			pComp->SetScreenDecals(1023, true, 24007, 0);
		}

	} else if (m_Radiation < pLuaConst->K_RADIATION_HIGH_TIPS) {

		if (isShowLowRadiationTips) {
			isShowLowRadiationTips = false;

			RadiationComponent* pComp = m_ClientPlayer->getRadiationComponent();
			if (pComp) {
				pComp->SetScreenDecals(1023, false, 24007, 0);
			}
		}
	}

	float r1 = getRadiation();
	LOG_INFO("PlayerAttrib::radiationTick_Client(): uin = %d | body radiation = %.2f", m_ClientPlayer->getUin(), r1);
#endif
}

void PlayerAttrib::foodTick_Server()
{
	float d = calculateTotalModification(PlayerAttributeType::Food);
	float v = m_FoodValue->GetValue();
	m_FoodValue->SetValue(v+d);
	// LOG_INFO("PlayerAttrib::foodTick(): uin = %d | food = %.2f, delta = %.2f", m_ClientPlayer->getUin(), v, d);

	checkAttributeBuffs(PlayerAttributeType::Food);

	// 只有当整数值变化幅度超过1时才同步
	int currentFoodValue = (int)(v + d);
	if (abs(currentFoodValue - m_lastFoodValue) >= 1)
	{
		m_ClientPlayer->syncAttr(ATTRT_CUR_HUNGER, currentFoodValue);
		m_lastFoodValue = currentFoodValue;
	}
}

void PlayerAttrib::foodTick_Client()
{
#ifndef IWORLD_SERVER_BUILD
	static bool isShowLowFoodTips = false;
	float v = m_FoodValue->GetValue();
	ConstAtLua* pLuaConst = GetLuaInterfaceProxy().get_lua_const();	
	if (v <= pLuaConst->K_FOOD_LOW_TIPS && !isShowLowFoodTips) {
		showAttributeTips();
		isShowLowFoodTips = true;
	} else if (v > pLuaConst->K_FOOD_LOW_TIPS) {
		isShowLowFoodTips = false;
	}
#endif
}

void PlayerAttrib::damageArmor(float points, ClientActor *attacker)
{
	for (int i = 0; i < EQUIP_WEAPON; i++)
	{
		int id = damageEquipItemWithType((EQUIP_SLOT_TYPE)i, (int)points);
		if (id > 0 && attacker)
		{
			// 道具被破坏
			ObserverEvent_ActorItem obevent(attacker->getObjId(), id, 1);
			GetObserverEventManager().OnTriggerEvent("Item.Destroy", &obevent);
		}
	}
}

bool PlayerAttrib::attackedFrom(OneAttackData &atkdata, ClientActor *attacker /* = NULL */)
{
	if (m_OwnerActor->isInvulnerable(dynamic_cast<ClientPlayer*>(atkdata.fromplayer)))
	{
		//LOG_INFO("PlayerAttrib::attackedFrom(): false 1");
		return false;
	}

	if (!LivingAttrib::attackedFrom(atkdata, attacker))
	{
		//LOG_INFO("PlayerAttrib::attackedFrom(): false 2");
		return false;
	}

	if (atkdata.damage_armor) useStamina(STAMINA_HURT);
	return true;
}


void PlayerAttrib::SetCurBuildingId(int buildingId)
{
	m_CurBuilldingId = buildingId;
}

int PlayerAttrib::getCurBuildingId()
{
	return m_CurBuilldingId;
}

void PlayerAttrib::addHP(float hp, bool overflowable)
{
	// 如果玩家处于倒地状态，特殊处理
	if (m_pDownedStateAttrib && isPlayerDowned())
	{
		// 在倒地状态下不应受到普通治疗
		if (hp > 0)
			return;

		// 如果是伤害，则传递给倒地状态处理
		if (hp < 0)
			m_pDownedStateAttrib->onPlayerDamaged(-hp);

		return;
	}
	
	if ((m_Life + hp <= 0.0f) && !isPlayerDowned())
	{
		m_Life = 0.0f;
		ActorLiving* living = dynamic_cast<ActorLiving*>(m_OwnerActor);
		living->setHPProgressDirty();
		m_pDownedStateAttrib->enterDownedState();
		auto player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
		if (player) player->closeContainer();
		return;
	}

	m_StillnessTick = 0;
	LivingAttrib::addHP(hp, overflowable);
	LOG_INFO("PlayerAttrib::addHP(): uin = %d | HP = %.2f | change = %.2f", m_ClientPlayer->getUin(), m_Life, hp);
	if (m_Life > 0.0f)
	{
		m_pDownedStateAttrib->changeToNormalState();
	}
}

void PlayerAttrib::addHPDirectly(float hp)
{
	if (m_pDownedStateAttrib) {
		m_pDownedStateAttrib->reset();
		m_pDownedStateAttrib->changeToNormalState();
	}
	m_Life += hp;
	if (m_Life >= m_fMaxHP)
		m_Life = m_fMaxHP;

	m_FoodValue->SetFoodValue(m_Life);
	m_ThirstValue->SetThirstValue(m_Life);
}

float PlayerAttrib::getMaxHP()
{
	World* world = m_OwnerActor->getWorld();
#ifdef OLD_ATTRIBUTES
	//if (!world)
	//{
	//	return m_fMaxHP;
	//}
	//if (world->isRemoteMode())
	//{
	//	return m_fMaxHP;
	//}
	//float max = m_fBasicMaxHP;
	//max += LivingAttrib::getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_HP_MAX, max);

	//if (max <= 0.0f)
	//{
	//	max = 1.0f;
	//}
	//m_fMaxHP = max;
#else
	if (!world)
	{
		return mHPValue->GetMaxLimitValue();
	}
	if (!world->onServer())
	{
		return mHPValue->GetMaxLimitValue();
	}

	float max = mHPValue->GetBasicMaxHP();
	max += LivingAttrib::getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_HP_MAX, max);

	if (max <= 0.0f)
	{
		max = 1.0f;
	}
	mHPValue->SetMaxLimitValue(max);
#endif

	return m_fMaxHP;
}

void PlayerAttrib::setMoveSpeed(float fSpeed)
{
	m_Attribs[MODATTR_MOVE_SPEED].value = fSpeed;

	if (m_OwnerActor != g_pPlayerCtrl)
	{
		//ge GetGameEventQue().postPlayerAttrChange();
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
	}
}

void PlayerAttrib::onToggleGameMode()
{
	m_ItemAttMap.clear();
	setAttackType(-1);

	ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();

	//血量
#ifdef OLD_ATTRIBUTES
	m_fBasicMaxHP = lua_const->hpmax;
#else
	mHPValue->SetBasicMaxHP(lua_const->hpmax);
#endif
	m_fBasicOverflowHP = lua_const->hpoverflow;
	m_Life = lua_const->hpmax;
	m_fRecover = lua_const->default_xieliang_huifu_beilv;

	//体力
	m_fBasicMaxStrength = lua_const->strengthmax;
	m_fBasicOverflowStrength = lua_const->strengthoverflow;
	m_fStrength = lua_const->strengthmax;
	m_fStrengthRestoreFactor = lua_const->default_tili_huifu_beilv;
	m_fBasicStrengthRestore = 1.0f; //切换编辑模式 还原体力值恢复速度
	clearBuff();

	for (int i = 0; i < Actor_Speed_Type_Count; i++)
	{
		m_fSpeed[i] = -1.0f;   //各种速度值reset
		m_bSpeedProtected[i] = false;
	}

	for (int i = 0; i < 2; i++)
	{
		m_fBaseAttack[i] = -1.0f;
		m_fBaseArmor[i] = -1.0f;
	}
	// 重置血条、体力值
	//ge GetGameEventQue().postPlayerAttrReset();
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_RESET", MNSandbox::SandboxContext(nullptr));
}

bool PlayerAttrib::checkIfItemHasAttAction(int iItemid, int iAttType)
{
	auto it = m_ItemAttMap.find(iItemid);
	if (it != m_ItemAttMap.end()) {
		return (it->second & iAttType) > 0;
	}
	return false;
}

void PlayerAttrib::addItemAttAction(int itematt)
{
	m_ItemAttMap[itematt >> 9] = itematt & 0x1ff;
}

void PlayerAttrib::getItemAttAction(std::vector<int>& vItemAttList)
{
	for (auto it = m_ItemAttMap.begin(); it != m_ItemAttMap.end(); it++) {
		vItemAttList.push_back((it->first << 9) + it->second);
	}
}

float PlayerAttrib::getKnockback(ATTACK_TYPE atktype, ATTACK_TARGET_TYPE targettype)
{
	float knock = 0.0f;
	if (atktype < MAX_PHYSICS_ATTACK)
	{
		if (atktype == ATTACK_RANGE) knock = 0.5f;
		else knock = 1.0f;

#if 0
		if (isNewStatus())
			knock += getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_KNOCK, knock);
		else
			knock += getModAttrib(MODATTR_KNOCK);
#else
		knock += getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_KNOCK, knock);
#endif
		knock += getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_KNOCK, atktype, targettype);
	}
	if (knock > 30.0f) { knock = 30.0f; }

	return knock;
}

void PlayerAttrib::setItemAttAction(int iItemid, int iAttType, bool bActive)
{
	auto it = m_ItemAttMap.find(iItemid);
	if (it != m_ItemAttMap.end()) {
		if (bActive)
			it->second |= iAttType;
		else
			it->second &= ~iAttType;
	}
	else if (bActive) {
		m_ItemAttMap[iItemid] = iAttType;
	}
}

void PlayerAttrib::setMaxThirst(float max)
{
	if (max > 0)
		m_ThirstValue->SetMaxThirst(max);
	//// 最大体力值变动 通知属性改变
	//if (m_ClientPlayer->hasUIControl())
	//{
	//	//ge GetGameEventQue().postPlayerAttrChange();
	//	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
	//	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
	//		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
	//}

	//if (g_WorldMgr && m_OwnerActor)
	//	g_WorldMgr->signChangedToSync(m_OwnerActor->getObjId(), BIS_THIRST_MAX);
}

void PlayerAttrib::setThirstOnlyInform(float value)
{
	// //int old = m_fThirst;
	// //m_fThirst = value;
	// int old = m_ThirstValue->GetThirstValue();
	// m_ThirstValue->SetThirstValue(value);
	// if (!m_ClientPlayer->getWorld() || !m_ClientPlayer->getWorld()->isRemoteMode())
	// 	m_ClientPlayer->syncAttr(ATTRT_CUR_THIRST, value);

	// checkThirstBuff();

	// //仅整数部分变化时下发通知
	// if (m_ClientPlayer->hasUIControl() && int(value) != old)
	// {
	// 	//ge GameEventQue::GetInstance().postPlayerAttrChange();
	// 	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
	// 	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
	// 		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
	// }

	// checkPersistentOperation();
}

void PlayerAttrib::addPerseverance(float val)
{
	if (!GetLuaInterfaceProxy().shouldUseNewHpRule())
	{
		return;
	}
	ActorAttribExecute* ex = getExtraExecute(ActorAttribType_Perseverance);
	if (!ex)
	{
		ex = SANDBOX_NEW(ActirPerseveranceExecute);
		m_attribExecute[ActorAttribType_Perseverance] = ex;
	}
	ex->ExecuteAdd(this, val, false, ActorAttribType_Perseverance);
}

void PlayerAttrib::setPerseverance(float val, bool force)
{
	if (!GetLuaInterfaceProxy().shouldUseNewHpRule())
	{
		return;
	}
	if (!force)
	{
		float oldval = getPerseverance();
		//设置规则, 高的覆盖低的
		if (val <= oldval)
		{
			return;
		}

	}
	ActorAttribExecute* ex = getExtraExecute(ActorAttribType_Perseverance);
	if (!ex)
	{
		ex = SANDBOX_NEW(ActirPerseveranceExecute);
		m_attribExecute[ActorAttribType_Perseverance] = ex;
	}
	ex->ExecuteSet(this, val, false, ActorAttribType_Perseverance);
}

float PlayerAttrib::getPerseverance()
{
	ActorAttribExecute* ex = getExtraExecute(ActorAttribType_Perseverance);
	if (!ex)
	{
		return 0;
	}
	return ((ActirPerseveranceExecute*)ex)->getValue();
}

float PlayerAttrib::getPlayerAttribute(PlayerAttributeType type)
{
	switch (type) {
		case PlayerAttributeType::Life:
			return getHP();
		case PlayerAttributeType::Strength:
			return getStrength();
		case PlayerAttributeType::Food:
			return getFood();
		case PlayerAttributeType::Thirst:
			return getThirst();
		case PlayerAttributeType::Temperature:
			return getTemperature();
		case PlayerAttributeType::Radiation:
			return getRadiation();
		default:
			return 0;
	}
}

bool PlayerAttrib::shouldApplyBuff(int buffId, int buffLevel)
{
    int iRealStatusId = GetDefManagerProxy()->getRealStatusId(buffId, buffLevel);
    auto def = GetDefManagerProxy()->getStatusDef(iRealStatusId);
    if (!def) return true; 
    
    for (int i = 0; i < MAX_BUFF_ATTRIBS; i++) {
        if (def->Status.EffInfo[i].CopyID == 0) continue;
        
        auto effDef = GetDefManagerProxy()->getBuffEffectDef(def->Status.EffInfo[i].CopyID);
        if (!effDef) continue;
        
        switch (effDef->AttType) {
            case BuffAttrType::BUFFATTRT_CONTINUE_CHG_HP:
            case BuffAttrType::BUFFATTRT_CONTINUE_CHG_PER_HP:
                if (getHP() >= getMaxHP() || getHP() <= 0.0f) {
                    return false;
                }
                break;
                
            case BuffAttrType::BUFFATTRT_CONTINUE_CHG_HUNGER:
                if (getFood() >= getMaxFood() || getFood() < 0.0f) {
                    return false; 
                }
                break;

			//case BuffAttrType::BUFFATTRT_CONTINUE_CHG_THIRST:
			//	// 如果是加口渴度的buff，检查当前口渴度
			//	if (getThirst() >= getMaxThirst()) {
			//		return false; // 口渴度已满，不需要增加口渴度buff
			//	}
			//	break;

			default:
				break;
        }
    }
    
    return true;
}

void PlayerAttrib::checkAttributeBuffs(const PlayerAttributeType type)
{
    const PlayerAttribCsvDef* config = GetDefManagerProxy()->getPlayerAttribCsvDef(type);
    if (!config) return;

	float currentValue = getPlayerAttribute(type);
    const float FLOAT_EPSILON = 0.0001f; // 浮点数比较精度
    
	// 检查debuff
	bool foundDebuff = false;
	for (const auto& debuff : config->Debuffs) {
		if (currentValue < debuff.threshold || Abs(currentValue - debuff.threshold) < FLOAT_EPSILON) {
			// 找到第一个满足条件的debuff
			if (!hasBuff(debuff.id, debuff.level)) {
				// 移除所有其他debuff
				for (const auto& otherDebuff : config->Debuffs) {
					if (otherDebuff.id != debuff.id) {
						removeBuff(otherDebuff.id);
					}
				}
				// 添加新的debuff
				addBuff(debuff.id, debuff.level);
			}
			foundDebuff = true;
			break;
		}
	}
	
	// 如果没有找到满足条件的debuff，移除所有debuff
	if (!foundDebuff) {
		for (const auto& debuff : config->Debuffs) {
			removeBuff(debuff.id);
		}
	}

	// 检查buff
	bool foundBuff = false;
	for (const auto& buff : config->Buffs) {
		if (currentValue > buff.threshold || Abs(currentValue - buff.threshold) < FLOAT_EPSILON) {
			// 找到第一个满足条件的buff
			if (!hasBuff(buff.id, buff.level) && shouldApplyBuff(buff.id, buff.level)) {
				// 移除所有其他buff
				for (const auto& otherBuff : config->Buffs) {
					if (otherBuff.id != buff.id) {
						removeBuff(otherBuff.id);
					}
				}
				// 添加新的buff
				addBuff(buff.id, buff.level);
			}
			foundBuff = true;
			break;
		}
	}
	
	// 如果没有找到满足条件的buff，移除所有buff
	if (!foundBuff) {
		for (const auto& buff : config->Buffs) {
			removeBuff(buff.id);
		}
	}
}

void PlayerAttrib::showAttributeTips()
{
	if (g_pPlayerCtrl) {
		std::string msg = "";
		ConstAtLua* pLuaConst = GetLuaInterfaceProxy().get_lua_const();
		int level = 3;
		float duration = pLuaConst->K_TIPS_SHOWDURATION;
		float temp = getTemperature();
		float rad = getRadiation();
		if (getHP() <= pLuaConst->K_HP_LOW_TIPS) {
			msg = GetDefManagerProxy()->getStringDef(10000150);
			g_pPlayerCtrl->ShowGameSocTips(msg, duration, level);
		}
		else if (getFood() <= pLuaConst->K_FOOD_LOW_TIPS) {
			msg = GetDefManagerProxy()->getStringDef(10000151);
		}
		else if (getThirst() <= pLuaConst->K_THIRST_LOW_TIPS) {
			msg = GetDefManagerProxy()->getStringDef(10000152);
		}
		else if (temp <= pLuaConst->K_TEMPERATURE_TOO_LOW_TIPS) {
			msg = GetDefManagerProxy()->getStringDef(10000154);
		}
		else if (temp <= pLuaConst->K_TEMPERATURE_LOW_TIPS) {
			msg = GetDefManagerProxy()->getStringDef(10000153);
		}
		else if (temp >= pLuaConst->K_TEMPERATURE_TOO_HIGH_TIPS) {
			msg = GetDefManagerProxy()->getStringDef(10000155);
		}
		else if (temp >= pLuaConst->K_TEMPERATURE_HIGH_TIPS) {
			msg = GetDefManagerProxy()->getStringDef(10000156);
		}
		else if (rad >= pLuaConst->K_RADIATION_TOO_HIGH_TIPS) {
			msg = GetDefManagerProxy()->getStringDef(10000158);
		}
		else if (rad >= pLuaConst->K_RADIATION_HIGH_TIPS) {
			msg = GetDefManagerProxy()->getStringDef(10000157);
		}

		if (!msg.empty()) {
			g_pPlayerCtrl->ShowGameSocTips(msg, duration, level);
		}
	}
}

void PlayerAttrib::checkStrengthBuff()
{
	// bool isEnterHomeLand = (g_WorldMgr && g_WorldMgr->getSpecialType() == HOME_GARDEN_WORLD);
	// if (m_fStrength <= m_fMaxStrengthForExhaustion && !isEnterHomeLand)
	// {
	// 	m_bIsExhausted = true;
	// 	addBuff(93, 1);
	// }
	// else
	// {
	// 	m_bIsExhausted = false;
	// 	removeBuff(93);
	// }
	// if (GetLuaInterfaceProxy().shouldUseNewHpRule())
	// {
	// 	if (m_fStrength <= m_minOverDraw)
	// 	{
	// 		addBuff(93, 2);
	// 	}
	// 	else if (m_fStrength > m_maxOverDraw && hasBuff(93, 2)) //加个判断的条件,避免死循环
	// 	{
	// 		removeBuff(93);
	// 		//这里在检测一遍93001buff 是否可以加上
	// 		checkStrengthBuff();
	// 	}
	// }
}


void PlayerAttrib::checkThirstBuff()
{
	//bool isEnterHomeLand = (g_WorldMgr && g_WorldMgr->getSpecialType() == HOME_GARDEN_WORLD);
	//if (m_ThirstValue->GetThirstValue() <= m_fMaxStrengthForExhaustion && !isEnterHomeLand)
	//{
	//	m_bIsExhausted = true;
	//	addBuff(113, 1);
	//}
	//else
	//{
	//	m_bIsExhausted = false;
	//	removeBuff(113);
	//}
	//if (GetLuaInterfaceProxy().shouldUseNewHpRule())
	//{
	//	if (m_ThirstValue->GetThirstValue() <= m_minOverDraw)
	//	{
	//		addBuff(113, 2);
	//	}
	//	else if (m_ThirstValue->GetThirstValue() > m_maxOverDraw && hasBuff(113, 2)) //加个判断的条件,避免死循环
	//	{
	//		removeBuff(113);
	//		//这里在检测一遍113001buff 是否可以加上
	//		checkThirstBuff();
	//	}
	//}
}

float PlayerAttrib::getReviveHp()
{
#ifdef OLD_ATTRIBUTES
	if (GetLuaInterfaceProxy().shouldUseNewHpRule())
	{
		return m_fMaxHP * (m_actor_revive_hp / 100.f);
	}
	return m_fMaxHP;
#else
	if (GetLuaInterfaceProxy().shouldUseNewHpRule())
	{
		return mHPValue->GetMaxLimitValue() * (m_actor_revive_hp / 100.f);
	}
	return mHPValue->GetMaxLimitValue();
#endif
}

float PlayerAttrib::getReviveStrength()
{
	if (GetLuaInterfaceProxy().shouldUseNewHpRule())
	{
		return m_fMaxStrength * (m_actor_revive_strength / 100.f);
	}
	return m_fMaxStrength;
}
void PlayerAttrib::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	//ClientPlayer *pOwner = dynamic_cast<ClientPlayer*>(owner);
	//m_ClientPlayer = pOwner;
	//m_Backpack = SANDBOX_NEW(BackPack, pOwner);
	Super::OnEnterOwner(owner);
}
void PlayerAttrib::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	m_ClientPlayer = nullptr;
	Super::OnLeaveOwner(owner);
}

namespace AntiSetting {
	float GetRuntimeFloat();
}

/**
 * @brief 设置各种Speed属性, 同时对属性值进行混淆, 防止被外挂发现内存值
 * 
 * @param type speed类型(0 is walk, 1 is run, 2 is sneak, 3 is swim, 4 is jump)
 * @param v 属性值
 */
void PlayerAttrib::setSpeedAtt(int type, float v)
{
	Super::setSpeedAtt(type, v);
	if (AntiSetting::gCheatConfig.SwitchJumpHeight && isValidSpeedType(type)) {
		m_fSpeedProtect[type] = v + AntiSetting::GetRuntimeFloat() + type;
		m_bSpeedProtected[type] = true;
	}
}

/**
 * @brief 获取各种Speed属性
 * 
 * @param type speed类型(0 is walk, 1 is run, 2 is sneak, 3 is swim, 4 is jump)
 * @return type对应的属性值
 */
float PlayerAttrib::getSpeedAtt(int type)
{
	if (AntiSetting::gCheatConfig.SwitchJumpHeight && isValidSpeedType(type) && m_bSpeedProtected[type]) { 
		return m_fSpeedProtect[type] - AntiSetting::GetRuntimeFloat() - type;
	} else {
		return Super::getSpeedAtt(type);
	}
}

void PlayerAttrib::setAttrShapeShift(bool change,int mobid /* =0 */)
{
	if (m_isAttrShapeShift != change)
	{
		if (change)
		{
			if (mobid == 0) return;
			m_AttrShapeShiftTick = 3;
			MonsterDef* def = GetDefManagerProxy()->getMonsterDef(mobid);
			if (!def) return;
			m_AttrShapeShiftDef = def;

			m_oldLife = m_Life;
#ifdef OLD_ATTRIBUTES
			m_oldBasicMaxHP = m_fBasicMaxHP;
#else
			m_oldBasicMaxHP = mHPValue->GetBasicMaxHP();
#endif
			m_oldStrength = m_fStrength;
			m_oldBasicMaxStrength = m_fBasicMaxStrength;
			m_oldFoodLevel = m_FoodLevel;

			setBasicMaxHP(def->Life);
			setHP(def->Life);
			setBasicMaxStrength(0);
			setStrength(0);

			PlayerControl* pc = dynamic_cast<PlayerControl*>(m_ClientPlayer);
			if (pc && pc->m_playerViewMask)
			{
				pc->m_playerViewMask->setAttrShapeShift(true);
			}
		}
		else
		{
			if (m_AttrShapeShiftTick != 1)
			{
				m_AttrShapeShiftTick = 0;// 需要下一帧处理，不然buff会崩溃
				return;
			}
			m_AttrShapeShiftTick = 2;
			clearBuff(true);
			for (size_t i = 0; i < m_oldBuffs.size(); i++) {
				ActorBuff buff = m_oldBuffs[i];
				addBuff(buff.buffid, buff.bufflv, buff.ticks);
			}
			m_oldBuffs.clear();

			m_AttrShapeShiftDef = NULL;
			setBasicMaxHP(m_oldBasicMaxHP);
			setHP(m_oldLife);
			setBasicMaxStrength(m_oldBasicMaxStrength);
			setStrength(m_oldStrength);
			PlayerControl* pc = dynamic_cast<PlayerControl*>(m_ClientPlayer);
			if (pc && pc->m_playerViewMask)
			{
				pc->m_playerViewMask->setAttrShapeShift(false);
			}
		}

		m_isAttrShapeShift = change;// 这句要在最末尾执行

		MINIW::ScriptVM::game()->callFunction("SetMainUIState", NULL);//刷新一下ui条
	}
}

bool PlayerAttrib::isHasAttrShapeShiftBuff(int buffid, int bufflv)
{
	int iRealStatusId = GetDefManagerProxy()->getRealStatusId(buffid, bufflv);
	auto def = GetDefManagerProxy()->getStatusDef(iRealStatusId);
	if (!def) return false;
	for (int j = 0; j < MAX_BUFF_ATTRIBS; j++)
	{
		auto effDef = GetDefManagerProxy()->getBuffEffectDef(def->Status.EffInfo[j].CopyID);
		if (effDef && effDef->AttType == BUFFATTRT_ATTR_SHAPESHIFT)  //如果是属性变身
		{
			if (m_isAttrShapeShift) return true;// 已经具有属性变身了，不能生效这个buff
			int val = def->Status.EffInfo[j].Value[0];
			if (val > 0)
			{
				// 需要在这里先复制一份新得m_buffs
				m_oldBuffs = m_Buffs;
				/*std::vector<ActorBuff> buffs;
				m_Buffs = buffs;*/
				clearBuff(true);
				setAttrShapeShift(true, val);
				return false;
			}
		}
	}
	return false;
}

void PlayerAttrib::waterPressureTick()
{
	if (m_LowerBodyWaterPressure > 0)// 下半身水压
	{
		m_Lowerm_WaterPressureTick++;
		if (m_Lowerm_WaterPressureTick > 5 * 20)// 装备以【损耗速度】 /  5秒扣除当前耐久度
		{
			m_Lowerm_WaterPressureTick = 0;

			for (int i = 1; i < EQUIP_WEAPON; i++)
			{
				BackPackGrid* grid = getEquipGrid((EQUIP_SLOT_TYPE)i);
				if (grid && grid->def)
				{
					const ToolDef* tool = GetDefManagerProxy()->getToolDef(grid->def->ID);
					if (tool)
					{
						if (tool->ResPressureLevel < m_LowerBodyWaterPressure)
						{
							//  损耗速度 = （水压等级-耐压阈值）* 压力消耗耐久,
							int val = (m_LowerBodyWaterPressure - tool->ResPressureLevel) * tool->PressDuration;
							damageEquipItemWithType((EQUIP_SLOT_TYPE)i, val);
						}
					}
				}
			}
		}

	}
	else
	{
		m_Lowerm_WaterPressureTick = 0;
	}

	if (m_iWaterPressure > 0)// 上半身水压
	{
		m_WaterPressureTick++;
		if (m_WaterPressureTick > 5 * 20)// 装备以【损耗速度】 /  5秒扣除当前耐久度
		{
			m_WaterPressureTick = 0;

			BackPackGrid* grid = getEquipGridWithType(EQUIP_HEAD);
			if (grid && grid->def)
			{
				const ToolDef* tool = GetDefManagerProxy()->getToolDef(grid->def->ID);
				if (tool)
				{
					if (tool->ResPressureLevel < m_iWaterPressure)
					{
						int val = (m_iWaterPressure - tool->ResPressureLevel) * tool->PressDuration;
						damageEquipItemWithType(EQUIP_HEAD, val);
					}
				}
			}
		}

	}
	else
	{
		m_WaterPressureTick = 0;
	}
}

//口渴的计算
void PlayerAttrib::thirstTick_Server()
{
	float d = calculateTotalModification(PlayerAttributeType::Thirst);
	float v = m_ThirstValue->GetThirstValue();
	
	m_ThirstValue->SetThirstValue(v + d);
	//LOG_INFO("PlayerAttrib::thirstTick(): uin = %d | thirst = %.2f, delta = %.2f", m_ClientPlayer->getUin(), v, d);

	checkAttributeBuffs(PlayerAttributeType::Thirst);

	// 只有当整数值变化幅度超过1时才同步
	int currentThirstValue = (int)(v + d);
	if (abs(currentThirstValue - m_lastThirstValue) >= 1)
	{
		m_ClientPlayer->syncAttr(ATTRT_CUR_THIRST, currentThirstValue);
		m_lastThirstValue = currentThirstValue;
	}
}

void PlayerAttrib::thirstTick_Client()
{
#ifndef IWORLD_SERVER_BUILD
	static bool isShowLowThirstTips = false;
	float v = m_ThirstValue->GetThirstValue();
	ConstAtLua* pLuaConst = GetLuaInterfaceProxy().get_lua_const();
	if (v <= pLuaConst->K_THIRST_LOW_TIPS) {
		showAttributeTips();
		isShowLowThirstTips = true;
	} else if (v > pLuaConst->K_THIRST_LOW_TIPS) {
		isShowLowThirstTips = false;
	}
#endif
}

void PlayerAttrib::thirstRestoreTick()
{
	PlayerStateManager& playerStateManager = m_ClientPlayer->getPlayStateManager();
	float restoreFactor = m_fStrengthRestoreFactor;
	if (restoreFactor < 0)
			restoreFactor = (float)GetLuaInterfaceProxy().get_lua_const()->default_tili_huifu_beilv;

	restoreFactor += getGeniusValue(GENIUS_SURVIVE, 0);

	const float restore = m_fBasicStrengthRestore * restoreFactor;
	float extraThirst= 0;
	if (playerStateManager.getState(PLAYERSTTYPE_MOVE))
	{
		if (playerStateManager.getState(PLAYERSTTYPE_SNEAK))
		{
			extraThirst = getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_SNEAK_RECOVER_STRENGTH, restore);
		}
		else
		{
			extraThirst = getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_WALK_RECOVER_STRENGTH, restore);
		}
	}
	else if (playerStateManager.getState(PLAYERSTTYPE_STOP))
	{
		extraThirst = getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_STOP_RECOVER, restore);
	}
	const float allrestoreBuff = getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_ALL_SPEND_STRENGTH, restore);
	const float change = restore + extraThirst + allrestoreBuff;
	addThirst(change);
}

void PlayerAttrib::addFoodLevel(float increment, bool isSendMsg)
{
	if (Abs(increment) <= 0.01f)
	{
		return;
	}

	//LOG_INFO("PlayerAttrib::addFoodLevel(): uin = %d | food = %.2f | increment = %.2f", m_ClientPlayer->getUin(), getFoodLevel(), increment);

	const int old = int(m_FoodValue->GetValue());
	m_FoodValue->SetValue(m_FoodValue->GetValue() + increment);

	if (m_ClientPlayer->getWorld() && m_ClientPlayer->getWorld()->onServer()) {
		// 只有当整数值变化幅度超过1时才同步
		if (fabs(m_FoodValue->GetValue() - old) >= 1) {
			m_ClientPlayer->syncAttr(ATTRT_CUR_HUNGER, m_FoodValue->GetValue());
		}
	}
	if (m_pDownedStateAttrib && isSendMsg) m_pDownedStateAttrib->changeToNormalState();
}


void PlayerAttrib::addThirst(float increment, bool isSendMsg)
{
	if (Abs(increment) <= 0.0001f) return;

	//for (auto p : m_ThirstExecute)
	//{
	//	auto ex = getExtraExecute(p);
	//	if (ex && ex->ExecuteAdd(this, increment, false, ActorAttribType_Thirst))
	//	{
	//		return;
	//	}
	//}
	LOG_INFO("PlayerAttrib::addThirst(): uin = %d | thirst = %.2f | increment = %.2f", m_ClientPlayer->getUin(), getThirst(), increment);
	setThirst(getThirst() + increment);

	if (m_pDownedStateAttrib && isSendMsg) 
		m_pDownedStateAttrib->changeToNormalState();
}

void PlayerAttrib::setBasicMaxThirst(float max)
{
	if (max > 0)
		m_ThirstValue->SetBasicMaxThirst(max);
}

void PlayerAttrib::setThirst(float value)
{
	const int old = int(m_ThirstValue->GetThirstValue());
	m_ThirstValue->SetThirstValue(value);

	if (m_ClientPlayer->getWorld() && m_ClientPlayer->getWorld()->onServer())
		m_ClientPlayer->syncAttr(ATTRT_CUR_THIRST, value);

	//仅整数部分变化时下发通知
	if (int(value) != old)
	{
		if (m_ClientPlayer->hasUIControl())
		{
			//ge GetGameEventQue().postPlayerAttrChange();
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
		}

		if (g_WorldMgr && m_OwnerActor)
			g_WorldMgr->signChangedToSync(m_OwnerActor->getObjId(), BIS_THIRST);
	}

	checkPersistentOperation();
}

bool PlayerAttrib::dropCurrentHandItem()
{
	if (!m_Backpack)
		return false;
	PackContainer* pack = (PackContainer*)m_Backpack->getContainer(m_Backpack->getShortcutStartIndex());
	auto& grid = pack->m_Grids[getCurShotcut()];
	auto dropComponent = m_OwnerActor->GetComponent<DropItemComponent>();
	if (!grid.isEmpty())
	{
		if (dropComponent)
		{
			dropComponent->dropItem(&grid);
		}
		grid.clear();
		pack->afterChangeGrid(grid.getIndex());
	}

	return true;
}

void PlayerAttrib::execEffect(BuffAttrType type, std::vector<StatusAttInfo>& vSAInfo)
{
	LivingAttrib::execEffect(type, vSAInfo);
	switch (type)
	{
	case BUFFATTRT_FORBID_OPERATE:
		//禁止操作生效时，检测玩家各种状态并关闭 by:Jeff
		if (m_ClientPlayer && m_ClientPlayer->getWorld() && vSAInfo.size() > 0)
		{
			if (m_ClientPlayer == g_pPlayerCtrl)
			{
				//关闭演奏界面
				MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("close_main_player_freeAutoGen", MNSandbox::SandboxContext(nullptr));

				//坐在物理载具驾驶座上或钢琴上时，下载具
				auto RidComp = m_ClientPlayer->getRiddenComponent();
				if (g_pPlayerCtrl->isSittingInPianoChair() || (RidComp && RidComp->isVehiclePassenger()))
					g_pPlayerCtrl->dismountActor();
			}

			//if (m_ClientPlayer->isSittingInPianoChair())
			//{
			//	m_ClientPlayer->standUpFromChair();
			// 
			//}

			////坐在物理载具驾驶座上时，下载具
			//auto RidComp = m_ClientPlayer->getRiddenComponent();
			//if (RidComp && !RidComp->isVehicleController())
			//{

			//}
		}

		break;

	default:
		break;
	}
}

// New function to calculate total modifications based on AttributeType
float PlayerAttrib::calculateTotalModification(PlayerAttributeType attributeType)
{
	PlayerStateManager& playerStateManager = m_ClientPlayer->getPlayStateManager();
	PlayerState* pState = m_ClientPlayer->getCurrentActionStatePtr();
	const auto def = GetDefManagerProxy()->getPlayerAttribCsvDef(attributeType);
	if (!pState || !def) return 0.0f;

	float delta = 0.0f;

	// Handle internalDecreace
	for (const auto& entry : def->InternalDecreace) {
		const auto& data = entry.second;
		delta -= (data.value);
	}

	std::map<int, float> costMap;
	if (attributeType == PlayerAttributeType::Food)
	{
		costMap = m_FoodCostMap;
	}
	else if (attributeType == PlayerAttributeType::Thirst)
	{
		costMap = m_ThirstCostMap;
	}	

	// 获取当前动作状态
	std::string actionState = pState->getStateID();
    bool isDigging = (actionState == "Dig" || actionState == "AttackBlock" || actionState == "ChargeDig");
    bool isShooting = (actionState == "ChargeAttack" || actionState == "GunUse");
	//LOG_INFO("actionState: %s", actionState.c_str());

	if (isDigging)
	{
		delta -= costMap[(int)PLAYERSTTYPE_MELEE];
		LOG_INFO("state: dig | delta: %f", delta);
		//return delta;
	}
	else if (isShooting)
	{
		delta -= costMap[(int)PLAYERSTTYPE_SHOOT];
		LOG_INFO("state: shoot | delta: %f", delta);
		//return delta;
	}

	// 再判断移动状态
	bool sleep = playerStateManager.getState(PLAYERSTTYPE_SLEEP);
	if (sleep)
	{
		delta -= costMap[(int)PLAYERSTTYPE_SLEEP];
		//LOG_INFO("state: sleep | delta: %f", delta);
		return delta;
	}

	bool swim = playerStateManager.getState(PLAYERSTTYPE_SWIM);
	if (swim)
	{
		delta -= costMap[(int)PLAYERSTTYPE_SWIM];
		//LOG_INFO("state: swim | delta: %f", delta);
		return delta;
	}

	bool jump = playerStateManager.getState(PLAYERSTTYPE_JUMP);
	if (jump)
	{
		delta -= costMap[(int)PLAYERSTTYPE_JUMP];
		//LOG_INFO("state: jump | delta: %f", delta);
		return delta;
	}

	bool sneak = playerStateManager.getState(PLAYERSTTYPE_SNEAK);
	if (sneak)
	{
		delta -= costMap[(int)PLAYERSTTYPE_SNEAK];
		//LOG_INFO("state: sneak | delta: %f", delta);
		return delta;
	}

	bool run = playerStateManager.getState(PLAYERSTTYPE_RUN);
	if (run)
	{
		delta -= costMap[(int)PLAYERSTTYPE_RUN];
		//LOG_INFO("state: run | delta: %f", delta);
		return delta;
	}

	bool walk = playerStateManager.getState(PLAYERSTTYPE_MOVE);
	if (walk)
	{
		delta -= costMap[(int)PLAYERSTTYPE_MOVE];
		//LOG_INFO("state: walk | delta: %f", delta);
		return delta;
	}

	bool idle =  playerStateManager.getState(PLAYERSTTYPE_STOP);
	if (idle)
	{
		delta -= costMap[(int)PLAYERSTTYPE_STOP];
		//LOG_INFO("state: stop | delta: %f", delta);
		return delta;
	}

	LOG_INFO("state: unknown | delta: %f", delta);

	return delta;
}

bool PlayerAttrib::isPlayerDowned()
{
	if (isDead())
	{
		return false;
	}
	// Check if the downed state attribute exists and if the player is in downed state
	//return m_pDownedStateAttrib && m_pDownedStateAttrib->isInDownedState();
	bool a = m_Life <= 0.0f && m_pDownedStateAttrib->getDownedHealth() > 0.0f;
	return a;
}

// 重写 setHP 方法，处理倒地状态和直接死亡的情况
void PlayerAttrib::setHP(float hp, bool overflowable)
{
	// 如果已经在倒地状态
	if (isPlayerDowned()&& hp < 0) {
		// 如果明确设置为 -1，则强制死亡
		if (hp == -1) {
			// 处理从倒地状态直接死亡
			if (m_pDownedStateAttrib) {
				m_pDownedStateAttrib->handlePlayerDeath();
			}
			// 调用父类方法设置为 -1，触发死亡
			ActorAttrib::setHP(-1, overflowable);
			return;
		}

		// 其他情况保持倒地状态，生命值为0
		m_Life = 0;
		return;
	}

	// 如果设置生命值为 -1，直接触发死亡，不进入倒地状态
	if (hp == -1) {
		m_Life = 1;
		ActorAttrib::setHP(-1, overflowable);
		return;
	}

	// 如果当前有生命，且新生命值为0
	if (m_Life > 0 && hp == 0) {
		// 检查是否应该进入倒地状态
		if (checkAndEnterDownedState(m_Life)) { // 使用当前生命值作为伤害量的估计
			// 已进入倒地状态，将HP设为0但不触发死亡逻辑
			m_Life = 0;
			return;
		}
	}

	// 正常情况，调用父类方法
	ActorAttrib::setHP(hp, overflowable);
}

void PlayerAttrib::addHPByTrueDamage(float hp, bool overflowable)
{
	World* world = m_OwnerActor->getWorld();
	if (world && !world->onServer())
	{
		// 在客户端，直接通过父类更新HP
		addHPByTrueDamage2(hp, overflowable);
		return;
	}

	// 如果玩家处于倒地状态，特殊处理
	if (m_pDownedStateAttrib && isPlayerDowned())
	{
		// 在倒地状态下不应受到普通治疗
		if (hp > 0)
			return;

		// 如果是伤害，则传递给倒地状态处理
		if (hp < 0)
			m_pDownedStateAttrib->onPlayerDamaged(-hp);

		return;
	}

	// 检查此伤害是否会使玩家生命值降至0或以下
	if ((m_Life + hp <= 0.0f) && !isPlayerDowned())
	{
		m_Life = 0.0f;
		ActorLiving* living = dynamic_cast<ActorLiving*>(m_OwnerActor);
		living->setHPProgressDirty();
		m_pDownedStateAttrib->enterDownedState();
		return;
	}

	// 正常情况，调用父类方法
	ActorAttrib::addHPByTrueDamage(hp, overflowable);
}

void PlayerAttrib::addHPByTrueDamage2(float hp, bool overflowable)
{
	bool islive = m_Life > 0;
	for (auto p : m_hpExecute)
	{
		//真实伤害跳过护甲
		if (p == ActorAttribType_Armor)
		{
			continue;
		}
		auto ex = getExtraExecute(p);
		if (ex && ex->ExecuteAdd(this, hp, overflowable, ActorAttribType_Hp))
		{
			return;
		}
	}

	float maxHP = getMaxHP();
	float limitHP;
	if (overflowable)
	{
		limitHP = getLimitHP();
	}
	else
	{
		//溢出则保留
		limitHP = m_Life > maxHP ? m_Life : maxHP;
	}

	if (m_OwnerActor)
	{
		ActorLiving* targetLiving = dynamic_cast<ActorLiving*>(m_OwnerActor);
		if (targetLiving && 0 != hp)
		{
			targetLiving->setHPProgressDirty();
		}

		if (!(hp >= 0 && m_Life >= limitHP)) //血量是满的  不显示加血效果
		{
			m_OwnerActor->addHPEffect(hp);
		}
	}
#ifdef OLD_ATTRIBUTES
	if (!overflowable && m_Life > m_fMaxHP && hp > 0) {
		hp = 0;
	}
#else
	if (!overflowable && m_Life > mHPValue->GetMaxLimitValue() && hp > 0)
	{
		hp = 0;
	}
#endif
	const int old = int(m_Life);
	m_Life += hp;
	GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_ACTOR_ATTRIB, SandBoxMgrTypeID::ACTOR_CHANGE_HP, m_OwnerActor->getObjId() & 0xffffffff, (char*)&hp, sizeof(hp));



	if (m_Life > limitHP) m_Life = limitHP;
	if (m_Life < 0) m_Life = 0;
#ifdef OLD_ATTRIBUTES
	if (m_Life > m_fMaxHP)
	{
		m_OwnerActor->syncAttr(ATTRT_CUR_OVERFLOWABLE_HP, m_Life);
	}
	else
	{
		m_OwnerActor->syncAttr(ATTRT_CUR_HP, m_Life);
	}
#else
	if (m_Life > mHPValue->GetMaxLimitValue())
	{
		m_OwnerActor->syncAttr(ATTRT_CUR_OVERFLOWABLE_HP, m_Life);
	}
	else
	{
		m_OwnerActor->syncAttr(ATTRT_CUR_HP, m_Life);
	}
#endif
	// 受到伤害在死亡之前执行，否则伤害事件将获取不到攻击者信息
	if (hp < 0 && m_OwnerActor)
	{
		auto triggerComponent = m_OwnerActor->getTriggerComponent();
		if (triggerComponent)
		{
			triggerComponent->beHurtOnTrigger(hp);
		}
		//auto attackedComponent = m_OwnerActor->getAttackedComponent();
		//if (attackedComponent)
		//{
		//	attackedComponent->recordAttacker(1);	// 1受伤
		//}
	}

	//仅整数部分变化时下发通知
	if (int(m_Life) != old)
	{
		if (m_OwnerActor == g_pPlayerCtrl)
		{
			//ge GetGameEventQue().postPlayerAttrChange();
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
		}


		if (g_WorldMgr && m_OwnerActor && m_OwnerActor->isPlayer())
			g_WorldMgr->signChangedToSync(m_OwnerActor->getObjId(), BIS_HP);

		if (m_OwnerActor) m_OwnerActor->Event2().Emit<int>("changeHPOnTrigger", old);
	}
}

bool PlayerAttrib::checkAndEnterDownedState(float damageAmount)
{
	// 1. 检查是否有倒地状态
	if (!m_pDownedStateAttrib)
		return false;

	// 2. 如果已经处于倒地状态，不需要再次进入
	if (isPlayerDowned())
		return false;

	// 3. 检查是否满足直接死亡的条件（伤害过大等）
	if (m_pDownedStateAttrib->checkDirectDeathConditions())
	{
		// 满足直接死亡条件，不进入倒地状态
		m_pDownedStateAttrib->handlePlayerDeath();
		return false;
	}

	// 4. 记录本次伤害值，便于倒地状态系统判断
	m_pDownedStateAttrib->onPlayerDamaged(damageAmount);

	// 5. 进入倒地状态
	m_pDownedStateAttrib->enterDownedState();

	// 6. 设置一个微小的生命值，防止其他系统判断为死亡
	setHP(0.1f, false);

	return true;
}

bool PlayerAttrib::isDead() 
{
	if (m_Life < 0)
	{
		return true;
	}
	bool a = m_Life <= 0 && m_pDownedStateAttrib->getDownedHealth() <= 0;
	return a;
 //   // 如果倒地系统存在且激活
 //   if (m_pDownedStateAttrib)
 //   {
 //       // 如果玩家处于倒地状态，则不认为已死亡（仍有救援机会）
 //       if (m_pDownedStateAttrib->isInDownedState() || m_pDownedStateAttrib->isBeingRevived())
 //           return false;
 //           
 //       // 如果玩家既不在倒地状态，HP又为0或以下，则已死亡
 //       if (m_Life <= 0)
 //           return true;
 //   }
	//else
	//{
	//	// 如果没有倒地系统，使用基类的死亡判断
	//	return  ActorAttrib::isDead();
	//}
 //   return false;
}
