enum AI_MOTION_TYPE {};
enum ITEM_MODELDISP_TYPE {};
enum ITEM_MESH_TYPE {};
enum EQUIP_SLOT_TYPE {};
enum ATTACK_TYPE {};
enum ATTACK_TARGET_TYPE {};

$#include "Core/defdata.h"
$#include "Core/CoreCommonDef.h"
$#include "Core/worldData/world_types.h"
$#include "Core/extend/custommodel/CustomModelData.h"
$#include "Core/actors/helper/ActorTypes.h"

$cfile "../SandboxEngine/Core/actors/helper/ActorComponent_Base.h"

$cfile "Core/actors/ActorManager.h"
$cfile "Core/actors/actorComponents/CosumeAccumComponent.h"
$cfile "Core/actors/actorComponents/GrowComponent.h"
$cfile "Core/actors/actorComponents/BreedComponent.h"
$cfile "Core/actors/actorComponents/DialogueComponent.h"
$cfile "Core/actors/actorComponents/PlotComponent.h"
$cfile "Core/actors/actorComponents/BaseTargetComponent.h"
$cfile "Core/actors/actorComponents/ToAttackTargetComponent.h"

$cfile "Core/actors/clientActor/ClientActor.h"
$cfile "Core/actors/generalActor/ClientActorBaseLua.h"
$cfile "Core/actors/generalActor/ClientActorLiving.h"
$cfile "Core/actors/generalActor/ClientMob.h"
$cfile "Core/actors/generalActor/ClientItem.h"
$cfile "Core/actors/generalActor/ActorBoss.h"
$cfile "Core/actors/generalActor/ClientActorProjectile.h"
$cfile "Core/actors/actorAttrib/ActorAttrib.h"
$cfile "Core/actors/actorAttrib/AttribTypes.h"
$cfile "Core/actors/actorAttrib/LivingAttrib.h"
$cfile "Core/actors/actorAttrib/MobAttrib.h"
$cfile "Core/actors/actorAttrib/PlayerAttrib.h"
$cfile "Core/actors/actorAttrib/VillagerAttrib.h"
$cfile "Core/actors/miniActor/ActorBoat.h"
$cfile "Core/actors/miniActor/ActorBasketBall.h"
$cfile "Core/actors/miniActor/ActorContainerMob.h"
$cfile "Core/actors/miniActor/ActorTrader.h"
$cfile "Core/actors/miniActor/ActorTrainCar.h"
$cfile "Core/actors/miniActor/ActorBall.h"
$cfile "Core/actors/miniActor/ActorHorse.h"
$cfile "Core/actors/miniActor/ActorPackHorse.h"
$cfile "Core/actors/miniActor/ActorStorageBoxHorse.h"
$cfile "Core/actors/miniActor/ActorVillager.h"
$cfile "Core/actors/miniActor/ActorGiant.h"
$cfile "Core/actors/miniActor/ActorCrab.h"
$cfile "Core/actors/miniActor/ActorHippocampus.h"
$cfile "Core/actors/miniActor/ClientVacantBoss.h"
$cfile "Core/actors/miniActor/ActorFireLordBoss.h"
$cfile "Core/actors/miniActor/ActorRocket.h"
$cfile "Core/actors/miniActor/ActorHalfGiant.h"
$cfile "Core/actors/miniActor/ActorPortal.h"
$cfile "Core/actors/miniActor/ActorDesertBusinessman.h"
$cfile "Core/actors/ClientActorManager.h"
$cfile "Core/actors/miniActor/ClientFlyMob.h"
$cfile "Core/actors/actorbody/ActorBody.h"

$cfile "Core/actors/miniActor/ActorRiverLantern.h"
$cfile "Core/actors/miniActor/ActorSandMan.h"
$cfile "Core/actors/locoMotionComponents/ActorLocoMotion.h"
$cfile "Core/actors/locoMotionComponents/LivingLocoMotion.h"
$cfile "Core/actors/locoMotionComponents/HorseLocomotion.h"
$cfile "Core/actors/locoMotionComponents/BasketBallLocoMotion.h"
#$cfile "Core/actors/locoMotionComponents/AquaticLocomotion.h"
#$cfile "Core/actors/locoMotionComponents/ArrowLocoMotion.h"
$cfile "Core/actors/locoMotionComponents/BallLocomotion.h"
#$cfile "Core/actors/locoMotionComponents/FallingLocoMotion.h"
#$cfile "Core/actors/locoMotionComponents/FlyLocomotion.h"
$cfile "Core/actors/locoMotionComponents/TrixenieLocomotion.h"
$cfile "Core/actors/locoMotionComponents/physicsLocoMotion/PhysicsLocoMotion.h"
$cfile "Core/actors/locoMotionComponents/physicsLocoMotion/PhysicsLocoMotionComponent.h"
$cfile "Core/actors/locoMotionComponents/physicsLocoMotion/ProjectileLocoMotion.h"
$cfile "Core/actors/locoMotionComponents/PumpkinHorseLocomotion.h"
$cfile "Core/actors/generalActor/ProjectileFactory.h"
$cfile "Core/actors/clientActor/components/SoundComponent.h"
$cfile "Core/actors/clientActor/components/FindComponent.h"
$cfile "Core/actors/clientActor/components/EffectComponent.h"
$cfile "Core/actors/clientActor/components/DropItemComponent.h"
$cfile "Core/actors/clientActor/components/DirectionByDirComponent.h"
$cfile "Core/actors/clientActor/components/RiddenComponent.h"
$cfile "Core/actors/clientActor/components/CarryComponent.h"
$cfile "Core/actors/clientActor/components/ActionAttrStateComponent.h"
$cfile "Core/actors/clientActor/components/FireBurnComponent.h"
$cfile "Core/actors/clientActor/components/FallComponent.h"
$cfile "Core/actors/clientActor/components/AttackedComponent.h"

$cfile "Core/actors/clientActor/ClientActorFuncWrapper.h"
$cfile "Core/actors/miniActor/ActorIslandBusinessman.h"
$cfile "Core/actors/miniActor/ActorPatrolMob.h"
$cfile "Core/actors/miniActor/ActorPirateShip.h"
$cfile "Core/actors/miniActor/ActorSeaSpiritGuarding.h"
$cfile "Core/actors/miniActor/ActorYak.h"
$cfile "Core/actors/miniActor/ActorSnowMan.h"
$cfile "Core/actors/miniActor/ActorPushSnowBall.h"
$cfile "Core/actors/miniActor/ActorSnowHare.h"
$cfile "Core/actors/miniActor/ActorTravelingTrader.h"
$cfile "Core/actors/actorComponents/ClientFlyComponent.h"
$cfile "Core/actors/actorComponents/ActorVision.h"
$cfile "Core/actors/actorComponents/VacantComponent.h"
$cfile "Core/actors/actorComponents/DissolveComponent.h"
$cfile "Core/actors/actorComponents/InWaterComponent.h"
$cfile "Core/actors/actorComponents/BaseGridContainer.h"
$cfile "Core/actors/actorComponents/EquipGridContainer.h"
$cfile "Core/actors/actorComponents/GridContainer.h"
$cfile "Core/actors/actorComponents/SocRevivePointComponent.h"
$cfile "Core/actors/actorComponents/LockCtrlComponent.h"
$cfile "Core/actors/actorComponents/OpenContainerComponent.h"


$using namespace Rainbow::UILib;
