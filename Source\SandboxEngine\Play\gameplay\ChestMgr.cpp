/********************************************************************************
 * ChestMgr
 * author: cloud
 * date: 2025-01-21
 ********************************************************************************/
 
#include "ChestMgr.h"
#include "world.h"
#include "WorldManager.h"
#include "Core/blocks/container_world.h"
#include <sstream>
#include <iomanip>
#include <fstream>
#include <random>
#include <cmath>
#include "MiniShared/utils/json.hpp"

#define CHESTMGR_DEBUG_LOG 1
#if CHESTMGR_DEBUG_LOG
#define CHESTMGR_LOG(...) \
    do { \
        WarningStringMsg("[ChestMgr] " __VA_ARGS__); \
    } while(0)
#else
#define CHESTMGR_LOG(...)
#endif

// refresh check interval, seconds
#define REFRESH_CHECK_INTERVAL 600  // 10分钟完全刷新一次
#define TIME_CHECK_INTERVAL 10      // 10秒检查一次时间

struct Point {
	double x, z;
};

// 判断新点p与已有点集的最小距离不少于r
bool is_far_enough(const std::vector<Point>& points, const Point& p, double r) {
	for (const auto& q : points) {
		double dx = p.x - q.x, dz = p.z - q.z;
		if (dx * dx + dz * dz < r * r)
			return false;
	}
	return true;
}

// 估算能生成N个点的大致最小距离r
double estimate_r(double width, double height, int target_num) {
	double area = width * height;
	double c = 5.0;
	return std::sqrt(area / (c * target_num));
}

// 支持任意区域的泊松盘采样
std::vector<Point> poisson_disk_sampling(double x_min, double x_max, double z_min, double z_max, double r, int k = 30, uint64_t seed = 0) {
	double width = x_max - x_min;
	double height = z_max - z_min;
	std::vector<Point> result;
	std::vector<Point> active_list;

	std::mt19937 gen(seed ? seed : std::random_device{}());
	std::uniform_real_distribution<> dis_x(x_min, x_max);
	std::uniform_real_distribution<> dis_z(z_min, z_max);

	// 随机放第一个点
	Point first = { dis_x(gen), dis_z(gen) };
	result.push_back(first);
	active_list.push_back(first);

	while (!active_list.empty()) {
		std::uniform_int_distribution<> dis_idx(0, active_list.size() - 1);
		int idx = dis_idx(gen);
		Point center = active_list[idx];
		bool found = false;
		for (int i = 0; i < k; ++i) {
			double angle = std::uniform_real_distribution<>(0, 2 * M_PI)(gen);
			double radius = r + std::uniform_real_distribution<>(0, r)(gen);
			Point p = { center.x + std::cos(angle) * radius, center.z + std::sin(angle) * radius };
			if (p.x < x_min || p.x > x_max || p.z < z_min || p.z > z_max)
				continue;
			if (is_far_enough(result, p, r)) {
				result.push_back(p);
				active_list.push_back(p);
				found = true;
			}
		}
		if (!found)
			active_list.erase(active_list.begin() + idx);
	}
	return result;
}

ChestManager::ChestManager(World* world, int mapSize)
    : m_world(world)
    , m_mapSize(mapSize)
{
    m_containerMgr = dynamic_cast<WorldContainerMgr*>(world->getContainerMgr());
    m_randGen.seed(std::random_device()());
    m_lastRefreshTime = std::chrono::steady_clock::now();
    m_lastTimeCheckTime = std::chrono::steady_clock::now();
    m_lastWorldTime = m_world->GetWorldMgr()->getWorldTime();
    m_lastGameDay = m_world->GetWorldMgr()->getWorldTimeDay(); // 记录当前游戏天数
    if (!world->isRemoteMode())
    {
        char path[256] = { 0 };
        std::string rootDir = Rainbow::GetFileManager().GetWritePathRoot();
        sprintf(path, "%s/data/w%lld/soc_building_chests.json", rootDir.c_str(), m_world->getOWID());
        m_buildingChestConfigPath = path;
        CHESTMGR_LOG("buildingChestConfigPath: %s", m_buildingChestConfigPath.c_str());

        initBuildingChestsInfo();
        CHESTMGR_LOG("build chest size = %d", m_BuildingChests.size());
        initRandomChestsInfo();
        CHESTMGR_LOG("random chest size = %d", m_RandomChests.size());
    }
}

ChestManager::~ChestManager()
{

}

void ChestManager::tick()
{
    //bool bUGC = m_world->GetWorldMgr()->isUGCMode();
    //if (bUGC) {
    //    return;
    //}
    if (m_world->isRemoteMode()) return;
    // 检查游戏天数是否变化，变化了就刷新建筑宝箱
    int currentGameDay = m_world->GetWorldMgr()->getWorldTimeDay();
    if (currentGameDay > m_lastGameDay) {
        CHESTMGR_LOG("New game day detected (Day %d), refreshing building chests", currentGameDay);
        clearBuildingChests();
        refreshBuildingChests(currentGameDay);
        m_lastGameDay = currentGameDay;
    }

    // 处理定时宝箱    
    int t = m_world->GetWorldMgr()->getWorldTime();
    // 3000等于游戏内1h，精确到10分钟，所以判断大于500
    if (t - m_lastWorldTime >= 500) {
        auto& chestSpawnTable = GetDefManagerProxy()->getChestSpawnTable();
        for (auto iter = chestSpawnTable.m_Records.begin(); iter != chestSpawnTable.m_Records.end(); ++iter) {
            const ChestSpawnDef& def = iter->second;
            refreshFixedTimeChest(def, t);
        }

        m_lastWorldTime = t;

        dumpChests();
    }   
}

void ChestManager::refreshFixedTimeChest(const ChestSpawnDef& def, int t)
{
    if (def.fixedSpawnRefreshTime.empty()) {
        CHESTMGR_LOG("Fixed spawn chest %d has no refresh time defined", def.id);
        return;
    }
    
    
    std::istringstream ss(def.fixedSpawnRefreshTime);
    int hour = -1, minute = -1;  // 初始化为无效值便于调试
    char delimiter = 0;

    int gameday = m_world->GetWorldMgr()->getWorldTimeDay();
    
    CHESTMGR_LOG("parsing time string: '%s' for chest id %d", def.fixedSpawnRefreshTime.c_str(), def.id);
    
    if (ss >> hour >> delimiter >> minute && delimiter == ':') {
        // 将时间转换为游戏tick，6:00为起始时间点
        // 如果小于6点，则认为是第二天的时间
        int adjustedHour = hour;
        if (hour < 6) {
            adjustedHour = hour + 24;  // 第二天的时间
        }
        int requestTime = (adjustedHour - 6) * 3000 + minute * 50;
        CHESTMGR_LOG("parsed time: hour=%d, minute=%d, adjustedHour=%d, requestTime=%d", 
                    hour, minute, adjustedHour, requestTime);
        if (t >= requestTime) {
            CHESTMGR_LOG("refresh fixed time chest id %d, time %d, requestTime %d", def.id, t, requestTime);
            // 遍历所有随机箱子，chunk已加载的刷新
			for (auto it = m_RandomChests.begin(); it != m_RandomChests.end(); ++it) {
				auto& chest = it->second;
				if (chest.isSpawned) {
					continue;
				}

                if (gameday == chest.deadDay) {
                    continue;
                }

				int chunkX = chest.pos.x >> 4;
				int chunkZ = chest.pos.z >> 4;

                if (!isChunkLoaded(chunkX, chunkZ)) continue;

                WCoord oldPos = chest.pos;
                if (chest.pos.y <= 0) {
                    chest.pos.y = m_world->getTopSolidOrLiquidBlock(chest.pos.x, chest.pos.z);
                }
				if (chest.pos.y <= 0) continue;

                // 如果y坐标发生了变化，需要更新map的key
                if (oldPos.y != chest.pos.y) {
                    m_RandomChests.erase(it);
                    auto result = m_RandomChests.insert({chest.pos, chest});
                    it = result.first;
                }

				if (isValidChestPosition(chest.pos, chest.chestId)) {
					int err = 0;
					bool b = spawnRandomChest(chest, err);
					if (b) {
						it->second.isSpawned = true;
                        it->second.spawnCount++;
                        it->second.deadDay = 0;
						CHESTMGR_LOG("spawn random chest id %d, position at %d, %d, %d", chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z);
					} else {
						CHESTMGR_LOG("spawn random chest id %d, position at %d, %d, %d failed (%d)", chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z, err);
					}
				}
			}
        }
    } else {
        CHESTMGR_LOG("Invalid refresh time format for chest %d: %s", def.id, def.fixedSpawnRefreshTime.c_str());
    }
}

bool ChestManager::isValidChestPosition(const WCoord& pos, int blockId) const 
{
    if (m_world->isBlockLiquid(pos)) return false;
    if (!m_world->isBlockSolid(pos.x, pos.y-1, pos.z)) return false;
    if (m_world->getBlockID(pos.x, pos.y-1, pos.z) == blockId) return false;
    
    return true;
}

bool ChestManager::spawnRandomChest(const SocChestInfo& chestInfo, int& err) 
{
    err = 0;

    m_containerMgr = dynamic_cast<WorldContainerMgr*>(m_world->getContainerMgr());
    if (!m_containerMgr) {
        err = -1;
        return false;
    }
    
    int chestId = chestInfo.chestId;
    CHESTMGR_LOG("spawnRandomChest id %d, position at %d, %d, %d", chestId, chestInfo.pos.x, chestInfo.pos.y, chestInfo.pos.z);
    if (chestId > 0) {
        bool b1 = m_world->setBlockAll(chestInfo.pos, chestId, 0, 3, true);
        if (b1) {
            WorldStorageBox* pBox = m_containerMgr->addDungeonChest(chestInfo.pos, chestId, nullptr);
            if (pBox) {
                pBox->setIsNeedDestroyWhenEmpty(true);
            }
            LOG_INFO_BUILD("#####SOC Map running record spawnChest id = %d pos x:%d, y:%d, z:%d", chestId, chestInfo.pos.x, chestInfo.pos.y, chestInfo.pos.z);
            return true;
        }
        else {
            err = -2;
        }
    }
    LOG_INFO_BUILD("#####SOC Map running record spawnChest fail");
    return false;
}

bool ChestManager::spawnBuildingChest(SocBuildingChest& chestInfo, int& err)
{
	err = 0;
	m_containerMgr = dynamic_cast<WorldContainerMgr*>(m_world->getContainerMgr());
	if (!m_containerMgr || !m_world) {
		err = -1;
		return false;
	}
	int chestId = 0;
	int chunkX = chestInfo.pos.x >> 4;
	int chunkZ = chestInfo.pos.z >> 4;
	chestId = generateBuildingChestId(chestInfo.blockId, chunkX, chunkZ);
	CHESTMGR_LOG("spawnBuildingChest chestId %d, pos %d, %d, %d", chestId, chestInfo.pos.x, chestInfo.pos.y, chestInfo.pos.z);

	if (chestId > 0) {
        chestInfo.chestId = chestId;
		bool b1 = m_world->setBlockAll(chestInfo.pos, chestId, 0, 3, true);
		if (b1) {
            CHESTMGR_LOG("spawnBuildingChest chestId %d, pos %d, %d, %d success", chestId, chestInfo.pos.x, chestInfo.pos.y, chestInfo.pos.z);
            //LOG_INFO_BUILD("#####SOC Map running record buildingChest id = %d pos x:%d, y:%d z:%d blockid = %d ", chestId, chestInfo.pos.x, chestInfo.pos.y, chestInfo.pos.z, chestInfo.blockId);
			WorldStorageBox* pBox = m_containerMgr->addDungeonChest(chestInfo.pos, chestId, nullptr);
            if (pBox) {
                pBox->setIsNeedDestroyWhenEmpty(true);
            }
            return true;
		}
		else {
            //m_world->replaceBlock(chestId, chestInfo.pos.x, chestInfo.pos.y, chestInfo.pos.z, 0);
            //m_world->destroyBlock(chestInfo.pos.x, chestInfo.pos.y, chestInfo.pos.z, false);
            // m_world->setBlockAll(chestInfo.pos, 0, 0, 3, true);
            // m_world->setBlockAll(chestInfo.pos, chestId, 0, 3, true);
            WorldStorageBox* pBox = m_containerMgr->addDungeonChest(chestInfo.pos, chestId, nullptr);
            if (pBox) {
                pBox->setIsNeedDestroyWhenEmpty(true);
            }
			//err = -3;
            // CHESTMGR_LOG("spawnBuildingChest chestId %d, pos %d, %d, %d failed, err = %d", chestId, chestInfo.pos.x, chestInfo.pos.y, chestInfo.pos.z, err);
            return true;
		}
	}
    // LOG_INFO_BUILD("#####SOC Map running record buildingChest fail");
	return false;
}

bool ChestManager::initRandomChestsInfo()
{
	double x_min = -m_mapSize/2, x_max = m_mapSize/2;
	double z_min = -m_mapSize/2, z_max = m_mapSize/2;
	int target_num = 800;

	double width = x_max - x_min;
	double height = z_max - z_min;
	double r = estimate_r(width, height, target_num);

	auto& chestSpawnTable = GetDefManagerProxy()->getChestSpawnTable();
	for (auto iter = chestSpawnTable.m_Records.begin(); iter != chestSpawnTable.m_Records.end(); ++iter) {
		const ChestSpawnDef& def = iter->second;

		// 使用基于世界ID和定义ID的固定种子，确保每次启动生成的坐标都一致
		uint64_t poissonSeed = m_world->getOWID() * 1000 + def.id;
		auto points = poisson_disk_sampling(x_min, x_max, z_min, z_max, r, 30, poissonSeed);

		if (points.size() > def.randomSpawnCountMax) {
			// 使用基于世界ID的固定种子，确保每次启动生成的坐标都一致
			std::mt19937 shuffleGen(m_world->getOWID() + def.id);
			std::shuffle(points.begin(), points.end(), shuffleGen);
			points.resize(def.randomSpawnCountMax);
		}
		
		CHESTMGR_LOG("init random chest info. type = %d, size = %d", def.id, points.size());

		for (const auto& p : points) {
            WCoord pos;
            pos.x = p.x;
            pos.y = 0;
            pos.z = p.z;

			SocChestInfo chest;
			chest.chestId = def.id;
			chest.pos = pos;
			chest.config = &def;
			chest.isSpawned = false;
            chest.spawnCount = 0;
            chest.deadDay = 0;

            m_RandomChests[pos] = chest;
		}
	}

    // dump all random chests
    // for (const auto& it : m_RandomChests) {
    //     CHESTMGR_LOG("init random chest info. id = %d, position at (%d, %d, %d)", it.second.chestId, it.second.pos.x, it.second.pos.y, it.second.pos.z);
    // }

    return true;
}

std::string ChestManager::getTerrainTypeAtPosition(const WCoord& pos) const 
{
    // TODO: 实现地形类型判断逻辑
    return "forest";
}

void ChestManager::onChunkLoaded(int chunkX, int chunkZ) 
{
    trySpawnChestsInChunk(chunkX, chunkZ);
}

bool ChestManager::isChunkLoaded(int chunkX, int chunkZ) const {
    Chunk* pChunk = m_world->getChunk(chunkX, chunkZ);
    return pChunk != nullptr;
}

void ChestManager::trySpawnChestsInChunk(int chunkX, int chunkZ) {

    if (m_world->isRemoteMode()) return;

    const int CHUNK_SIZE = 16;
    int minX = chunkX * CHUNK_SIZE;
    int maxX = minX + CHUNK_SIZE;
    int minZ = chunkZ * CHUNK_SIZE;
    int maxZ = minZ + CHUNK_SIZE;

    int gameday = m_world->GetWorldMgr()->getWorldTimeDay();

    // spawn building chest
    for (auto it = m_BuildingChests.begin(); it != m_BuildingChests.end(); ++it) {
        auto& chest = it->second;
        if (chest.isSpawned) {
            continue;
        }

        if (gameday == chest.deadDay) {
            continue;
        }

        if (chest.pos.x >= minX &&
            chest.pos.x < maxX &&
            chest.pos.z >= minZ &&
            chest.pos.z < maxZ) {

			int err = 0;
			bool success = spawnBuildingChest(chest, err);
			if (success) {
				chest.isSpawned = false;
                chest.spawnCount++;
                CHESTMGR_LOG("onchunkload: spawn building chest id %d, position at %d, %d, %d", chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z);
			} else {
                CHESTMGR_LOG("onchunkload: spawn building chest id %d, position at %d, %d, %d failed (%d)", chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z, err);
            }
        }
    }

    // spawn random chest
	for (auto it = m_RandomChests.begin(); it != m_RandomChests.end(); ++it) {
        auto& chest = it->second;
		if (chest.isSpawned) {
			continue;
		}

        if (gameday == chest.deadDay) {
            continue;
        }

		if (chest.pos.x >= minX &&
			chest.pos.x < maxX &&
			chest.pos.z >= minZ &&
			chest.pos.z < maxZ) {

			WCoord oldPos = chest.pos;
			chest.pos.y = m_world->getTopSolidOrLiquidBlock(chest.pos.x, chest.pos.z);
			if (chest.pos.y <= 0) continue;

			// 如果y坐标发生了变化，需要更新map的key
			if (oldPos.y != chest.pos.y) {
				m_RandomChests.erase(it);
				auto result = m_RandomChests.insert({chest.pos, chest});
				it = result.first;
			}

			if (isValidChestPosition(chest.pos, chest.chestId)) {
				int err = 0;
				bool b = spawnRandomChest(chest, err);
				if (b) {
					it->second.isSpawned = true;
                    it->second.spawnCount++;
                    it->second.deadDay = 0;
					CHESTMGR_LOG("onchunkload: spawn random chest id %d, position at %d, %d, %d", chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z);
				} else {
                    CHESTMGR_LOG("onchunkload: spawn random chest id %d, position at %d, %d, %d failed (%d)", chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z, err);
                }
			}
		}
	}
}

void ChestManager::onChunkUnloaded(int chunkX, int chunkZ) 
{
    //const int CHUNK_SIZE = 16;
    //int minX = chunkX * CHUNK_SIZE;
    //int maxX = minX + CHUNK_SIZE;
    //int minZ = chunkZ * CHUNK_SIZE;
    //int maxZ = minZ + CHUNK_SIZE;

    //for (auto& chest : m_chests) {
    //    if (chest.isSpawned && 
    //        chest.pos.x >= minX && chest.pos.x < maxX &&
    //        chest.pos.z >= minZ && chest.pos.z < maxZ) {
    //        
    //        chest.isSpawned = false;
    //        
    //        if (chest.isBuildingChest) {
    //            CHESTMGR_LOG("Marking building chest id %d at %d, %d, %d as unspawned due to chunk unload", 
    //                chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z);
    //        } else {
    //            CHESTMGR_LOG("Marking chest at %d, %d, %d as unspawned due to chunk unload", 
    //                chest.pos.x, chest.pos.y, chest.pos.z);
    //        }
    //    }
    //}
}

void ChestManager::onDestroyContainer(const WCoord& pos) 
{
    CHESTMGR_LOG("onDestroyContainer pos (%d, %d, %d)", pos.x, pos.y, pos.z);
    
    // check building chest
    auto it = m_BuildingChests.find(pos);
    if (it != m_BuildingChests.end()) {
        it->second.isSpawned = false;
        it->second.deadDay = m_world->GetWorldMgr()->getWorldTimeDay();
        CHESTMGR_LOG("onDestroyContainer buildingChest id %d, position %d, %d, %d", it->second.chestId, it->second.pos.x, it->second.pos.y, it->second.pos.z);
        return;
    }


    // check random chest
    auto it2 = m_RandomChests.find(pos);
    if (it2 != m_RandomChests.end()) {
        it2->second.isSpawned = false;
        it2->second.deadDay = m_world->GetWorldMgr()->getWorldTimeDay();
        CHESTMGR_LOG("onDestroyContainer randomChest id %d, position %d, %d, %d", it2->second.chestId, it2->second.pos.x, it2->second.pos.y, it2->second.pos.z);
        return;
    }
}

bool ChestManager::addBuildingChest(int blockId, const WCoord& pos) {
    
    SocBuildingChest chest;
    chest.pos = pos;
    chest.chestId = 0;
    chest.blockId = blockId;
    chest.isSpawned = false;
    chest.spawnCount = 0;
    chest.deadDay = 0;
    m_BuildingChests[pos] = chest;
    
    return true;
}

bool ChestManager::initBuildingChestsInfo() 
{    
    dynamic_array<UInt8> fileData;
    bool fileExists = Rainbow::GetFileManager().LoadFullPathFileAsBinary(m_buildingChestConfigPath.c_str(), fileData);
    
    if (!fileExists || fileData.empty()) {
        CHESTMGR_LOG("Failed to open building chest config file: %s", m_buildingChestConfigPath.c_str());
        return false;
    }
    
    try {
        std::string jsonStr(reinterpret_cast<const char*>(fileData.data()), fileData.size());
        nlohmann::json jsonData = nlohmann::json::parse(jsonStr);
        
        int buildingChestCount = 0;
        if (jsonData.contains("buildingChests") && jsonData["buildingChests"].is_array()) {
            
            for (const auto& item : jsonData["buildingChests"]) {
                SocBuildingChest chest;
                chest.chestId = 0;
                chest.spawnCount = 0;
                chest.deadDay = 0;
                chest.blockId = item.contains("blockId") ? item["blockId"].get<int>() : 0;
                chest.pos.x = item["posX"];
                chest.pos.y = item["posY"];
                chest.pos.z = item["posZ"];
                chest.isSpawned = false;

                m_BuildingChests[chest.pos] = chest;
                // CHESTMGR_LOG("Loaded building chest at position %d, %d, %d, blockId: %d", chest.pos.x, chest.pos.y, chest.pos.z, chest.blockId);
            }
        }
        
        CHESTMGR_LOG("Loaded %d building chests from %s", m_BuildingChests.size(), m_buildingChestConfigPath.c_str());
        return true;
    }
    catch (const std::exception& e) {
        CHESTMGR_LOG("Error parsing building chest config file: %s", e.what());
        return false;
    }
}

bool ChestManager::saveBuildingChestsToJson(const std::string& filePath) 
{
    std::string targetPath = filePath.empty() ? m_buildingChestConfigPath : filePath;
    if (targetPath.empty()) {
        CHESTMGR_LOG("No file path specified for saving building chest config");
        return false;
    }
    
    try {
        nlohmann::json jsonData;
        nlohmann::json chestsArray = nlohmann::json::array();
        
        int buildingChestCount = 0;
        for (const auto& it : m_BuildingChests) {
			const auto& chest = it.second;
			nlohmann::json chestJson;
			chestJson["blockId"] = chest.blockId;
			chestJson["posX"] = chest.pos.x;
			chestJson["posY"] = chest.pos.y;
			chestJson["posZ"] = chest.pos.z;

			chestsArray.push_back(chestJson);
			buildingChestCount++;
        }
        
        jsonData["buildingChests"] = chestsArray;
        
        std::string jsonStr = jsonData.dump(4);
        
        bool success = Rainbow::GetFileManager().SaveFile(targetPath, jsonStr.c_str(), jsonStr.size());
        if (!success) {
            CHESTMGR_LOG("Failed to save file: %s", targetPath.c_str());
            return false;
        }
        
        CHESTMGR_LOG("Saved %d building chests to %s", buildingChestCount, targetPath.c_str());
        return true;
    }
    catch (const std::exception& e) {
        CHESTMGR_LOG("Error saving building chest config: %s", e.what());
        return false;
    }
}

const std::vector<SocChestInfo>& ChestManager::getChests() const
{
    m_AllChests.clear();

    // 添加随机宝箱
    for (const auto& pair : m_RandomChests) {
        m_AllChests.push_back(pair.second);
    }

    // 添加建筑宝箱（转换为 SocChestInfo 格式）
    for (const auto& pair : m_BuildingChests) {
        const SocBuildingChest& buildingChest = pair.second;
        SocChestInfo chestInfo;
        chestInfo.pos = buildingChest.pos;
        chestInfo.chestId = buildingChest.chestId;
        chestInfo.spawnCount = buildingChest.spawnCount;
        chestInfo.deadDay = buildingChest.deadDay;
        chestInfo.isSpawned = buildingChest.isSpawned;
        chestInfo.config = nullptr; // 建筑宝箱没有配置信息
        m_AllChests.push_back(chestInfo);
    }

    return m_AllChests;
}


int ChestManager::generateBuildingChestId(int blockId, int chunkX, int chunkZ) 
{
    auto replaceInfo = GetDefManagerProxy()->getBuildReplaceDef(blockId);
    if (replaceInfo && m_world && m_world->GetWorldMgr()) {
        ChunkRandGen randGen;
        randGen.setSeed64(m_world->getChunkSeed(chunkX, chunkZ));
        return EcosysBuildHelp::randReplaceId(replaceInfo, randGen);
    }
    return 0;
}

void ChestManager::refreshBuildingChests(int gameday) 
{
    int buildingChestCount = 0;
   
    for (auto it = m_BuildingChests.begin(); it != m_BuildingChests.end(); ++it) {
        auto& chest = it->second;
        if (chest.isSpawned) {
            CHESTMGR_LOG("refreshBuildingChests: buildingChest id %d, position at %d, %d, %d is spawned. ignore.", chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z);
            continue;
        }

        if (gameday == chest.deadDay) {
            CHESTMGR_LOG("refreshBuildingChests: buildingChest id %d, position at %d, %d, %d. deadday == gameday. ignore.", chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z);
            continue;
        }
        
        int chunkX = chest.pos.x >> 4;
        int chunkZ = chest.pos.z >> 4;
        
        if (!isChunkLoaded(chunkX, chunkZ)) {
            CHESTMGR_LOG("refreshBuildingChests: buildingChest id %d, position at %d, %d, %d. chunk not loaded. ignore.", chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z);
            continue;
        }
        
        int err = 0;
        bool success = spawnBuildingChest(chest, err);
        if (success) {
            chest.isSpawned = false;
            chest.spawnCount++;
            buildingChestCount++;
        }
    }

    CHESTMGR_LOG("Refresh building chests finished. count = %d", buildingChestCount);
}

void ChestManager::dumpChests()
{
    int buildingChestCount = 0;
    int randomChestCount = 0;
    for (auto it = m_BuildingChests.begin(); it != m_BuildingChests.end(); ++it) {
        if (it->second.isSpawned) {
            buildingChestCount++;
        }
    }
    
    for (const auto& it : m_RandomChests) {
        if (it.second.isSpawned) {
            randomChestCount++;
        }
    }

    CHESTMGR_LOG("dumpChests buildingChestCount = %d, randomChestCount = %d", buildingChestCount, randomChestCount);
}

void ChestManager::clearBuildingChests() 
{
    if (!m_containerMgr) return;
    
    // CHESTMGR_LOG("Start clearn building chests for new day");
    
    // int removedCount = 0;
    // int checkedCount = 0;
    // for (auto it = m_BuildingChests.begin(); it != m_BuildingChests.end();) 
    // {
    //     auto& chest = it->second;
    //     if (chest.isSpawned) {
    //         int chunkX = chest.pos.x >> 4;
    //         int chunkZ = chest.pos.z >> 4;
            
    //         if (isChunkLoaded(chunkX, chunkZ)) {
    //             m_world->setBlockAir(chest.pos);
    //             m_containerMgr->destroyContainer(chest.pos, false);
    //             removedCount++;
    //             chest.isSpawned = false;
    //         } else {
    //             chest.isSpawned = false;
    //         }
    //         checkedCount++;
            
    //         ++it;
    //     } else {
    //         ++it;
    //     }
    // }
    
    // CHESTMGR_LOG("Removed %d building chests for new day, checked %d building chests", removedCount, checkedCount);
}
