﻿#include "BlockMultiBase.h"
#include "blocks/BlockMaterialMgr.h"
#include "SectionMesh.h"
#include "section.h"
#include "world.h"
#include "BlockDefCsv.h"
#include "WorldManager.h"

using namespace MINIW;
WCoord BlockMultiBase::changeWithDirection(const WCoord& blockpos, const int& direction)
{
	if (direction == DIR_NEG_X)
	{
		return WCoord(blockpos.z, blockpos.y, -blockpos.x);
	}
	else if (direction == DIR_POS_X)
	{
		return WCoord(-blockpos.z, blockpos.y, blockpos.x);
	}
	else if (direction == DIR_NEG_Z)
	{
		return WCoord(blockpos.x, blockpos.y, blockpos.z);
	}
	else if (direction == DIR_POS_Z)
	{
		return WCoord(-blockpos.x, blockpos.y, -blockpos.z);
	}
	return WCoord(0,0,0);
	// else if (direction == DIR_NEG_Y)
	// {
	// 	return blockpos;
	// }
	// else if (direction == DIR_POS_Y)
	// {
	// 	return WCoord(blockpos.x, -blockpos.y, blockpos.z);
	// }
}

void BlockMultiBase::getMultiBlockRange(std::vector<WCoord>& rangelist, int placeDir, const WCoord& MultiRange, const WCoord& blockpos, bool isIncludeSelf)
{
	int xstart = 0;
	int xend = 0;
	int ystart = 0;
	int yend = 0;
	int zstart = 0;
	int zend = 0;

	WCoord extendDir = changeWithDirection(MultiRange, placeDir);
	//其他模式
	if (extendDir.x >= 0) xstart = 0, xend = extendDir.x;
	else xstart = extendDir.x, xend = 0;
	if (extendDir.y >= 0) ystart = 0, yend = extendDir.y;
	else ystart = extendDir.y, yend = 0;
	if (extendDir.z >= 0) zstart = 0, zend = extendDir.z;
	else zstart = extendDir.z, zend = 0;

	for (int x = xstart; x <= xend; x++)
	{
		for (int y = ystart; y <= yend; y++)
		{
			for (int z = zstart; z <= zend; z++)
			{
				if ((x == 0 && y == 0 && z == 0) && !isIncludeSelf)
					continue;
				rangelist.push_back(WCoord(x, y, z));
			}
		}
	}
}

BlockMultiBase::BlockMultiBase()
{

}

BlockMultiBase::~BlockMultiBase()
{

}

void BlockMultiBase::setMultiBlockSize(const WCoord& BlockSize)
{
	m_MultiRange = BlockSize;
}

bool BlockMultiBase::isCoreBlock(World* pworld, const WCoord& blockpos)
{
	if (!pworld)
		return false;

	int blockData = pworld->getBlockData(blockpos);
	return (blockData & 4) == 0;
}

WCoord BlockMultiBase::getCoreBlockPos(World* pworld, const WCoord& blockpos)
{
	if (!pworld)
		return WCoord(0, -1, 0);

	const auto& block = pworld->getBlock(blockpos);
	int blockdata = block.getData();

	// 如果是核心方块，直接返回当前位置
	if (!(blockdata & 4))
	{
		return blockpos;
	}

	int direction = blockdata & 3;
	// 获取扩展方向
	WCoord extendDir = changeWithDirection(m_MultiRange, direction);

	// 如果没有有效的扩展方向，返回无效位置
	if (extendDir == WCoord::infinity)
	{
		return WCoord(0, -1, 0);
	}

	int blockId = block.getResID();
	int xExtend = abs(extendDir.x);
	int xDir = 1;
	if (extendDir.x > 0) xDir = -1;

	int zExtend = abs(extendDir.z);
	int zDir = 1;
	if (extendDir.z > 0) zDir = -1;

	int yExtend = abs(extendDir.y);
	int yDir = 1;
	if (extendDir.y > 0) yDir = -1;

	for (int x = 0; x <= xExtend; x++)
	{
		for (int y = 0; y <= yExtend; y++)
		{
			for (int z = 0; z <= zExtend; z++)
			{
				if (x == 0 && y == 0 && z == 0) continue;
				WCoord curPos = blockpos + WCoord(x * xDir, y * yDir, z * zDir);
				int tmpblockid = pworld->getBlockID(curPos);
				if (tmpblockid != blockId) break;
				if (!(pworld->getBlockData(curPos) & 4))
				{
					return curPos;
				}
			}
		}
	}

	// 如果上述方法都找不到核心方块，返回无效位置
	return WCoord(0, -1, 0);
}

WCoord BlockMultiBase::getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockId, int blockdata)
{
	if (!pworld)
		return WCoord(0, -1, 0);

	const auto& block = pworld->getBlock(blockpos);

	// 如果是核心方块，直接返回当前位置
	if (!(blockdata & 4))
	{
		return blockpos;
	}

	int direction = blockdata & 3;
	// 获取扩展方向
	WCoord extendDir = changeWithDirection(m_MultiRange, direction);

	// 如果没有有效的扩展方向，返回无效位置
	if (extendDir == WCoord::infinity)
	{
		return WCoord(0, -1, 0);
	}

	//int blockId = block.getResID();
	int xExtend = abs(extendDir.x);
	int xDir = 1;
	if (extendDir.x > 0) xDir = -1;

	int zExtend = abs(extendDir.z);
	int zDir = 1;
	if (extendDir.z > 0) zDir = -1;

	int yExtend = abs(extendDir.y);
	int yDir = 1;
	if (extendDir.y > 0) yDir = -1;

	for (int x = 0; x <= xExtend; x++)
	{
		for (int y = 0; y <= yExtend; y++)
		{
			for (int z = 0; z <= zExtend; z++)
			{
				if (x == 0 && y == 0 && z == 0) continue;
				WCoord curPos = blockpos + WCoord(x * xDir, y * yDir, z * zDir);
				int tmpblockid = pworld->getBlockID(curPos);
				if (tmpblockid != blockId) break;
				if (!(pworld->getBlockData(curPos) & 4))
				{
					return curPos;
				}
			}
		}
	}

	// 如果上述方法都找不到核心方块，返回无效位置
	return WCoord(0, -1, 0);
}

void BlockMultiBase::getMultiBlockRange(std::vector<WCoord>& rangelist, int placeDir, const WCoord& blockpos, bool isIncludeSelf)
{
	return getMultiBlockRange(rangelist, placeDir,m_MultiRange, blockpos, isIncludeSelf);
}
