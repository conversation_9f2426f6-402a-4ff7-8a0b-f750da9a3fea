﻿#pragma once

#include "SandboxEngine.h"
#include "OgreWCoord.h"

struct BlockDef;
class World;

class EXPORT_SANDBOXENGINE BlockMultiBase;
class BlockMultiBase//tolua_exports
{ //tolua_exports
public:
	BlockMultiBase();
	virtual ~BlockMultiBase();
	//void initMultiRange(const BlockDef* def);
	void setMultiBlockSize(const WCoord& BlockSize);
	virtual bool isCoreBlock(World* pworld, const WCoord& blockpos);
	WCoord getCoreBlockPos(World* pworld, const WCoord& blockpos);
	//onremove的时候调用
	WCoord getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockId, int blockdata);
	void getMultiBlockRange(std::vector<WCoord>& rangelist, int placeDir, const WCoord& blockpos, bool isIncludeSelf = false);
public:
	static WCoord changeWithDirection(const WCoord& blockpos, const int& direction);
	static void getMultiBlockRange(std::vector<WCoord>& rangelist, int placeDir,const WCoord& MultiRange, const WCoord& blockpos, bool isIncludeSelf = false);
protected:
	WCoord m_MultiRange = WCoord::zero;
}; //tolua_exports
